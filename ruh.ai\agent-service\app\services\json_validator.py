import json
import jsonschema
from jsonschema import Draft7Valida<PERSON>, ValidationError


def validate_schema(data, schema_path):
    """
    Validates JSON data (either from a dictionary or a file) against a given schema.

    Args:
        data (dict or str): JSON data to validate (either a dictionary or a file path).
        schema_path (str): Path to the JSON schema definition file.

    Returns:
        dict: The validated JSON data.

    Raises:
        FileNotFoundError: If the schema file is not found.
        jsonschema.exceptions.ValidationError: If the data is not valid according to the schema.
        jsonschema.exceptions.SchemaError: If the schema definition itself is invalid.
    """
    try:
        with open(schema_path, "r") as schema_file:
            schema_def = json.load(schema_file)  # Load the schema definition
    except FileNotFoundError:
        print(f"[ERROR] Schema definition file not found: {schema_path}")
        raise

    # If `data` is a JSON string, parse it
    if isinstance(data, str):
        try:
            data = json.loads(data)  # Convert JSON string to dictionary
        except json.JSONDecodeError:
            raise ValueError("[ERROR] Provided JSON string is not valid.")

    # If `data` is a file path, load JSON from file
    elif isinstance(data, dict):
        pass  # Already a dictionary, no need to modify

    else:
        raise TypeError("[ERROR] 'data' must be either a dictionary or a valid JSON string.")

    validator = Draft7Validator(schema_def)
    try:
        validator.validate(data)
        print("JSON IS VALIDATE AGAINS SCHEMA....")
        return data  # Return the validated data
    except ValidationError as e:
        print(f"[ERROR] JSON validation failed: {e.message}")
        raise
    except jsonschema.exceptions.SchemaError as e_schema:
        raise Exception(f"Schema definition is invalid: {e_schema}")