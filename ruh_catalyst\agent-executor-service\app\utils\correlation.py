# correlation.py
import uuid
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta


class CorrelationTracker:
    """
    Tracks correlation IDs for request/response matching.
    Manages pending requests and response correlation.
    """

    def __init__(self, timeout_seconds: int = 300):
        self.logger = logging.getLogger(__name__)
        self.pending_requests: Dict[str, Dict[str, Any]] = {}
        self.timeout_seconds = timeout_seconds
        self.logger.info("CorrelationTracker initialized.")

    def generate_correlation_id(self) -> str:
        """Generate a new correlation ID."""
        return str(uuid.uuid4())

    def track_request(
        self, 
        correlation_id: str, 
        request_data: Dict[str, Any],
        timeout_override: Optional[int] = None
    ) -> None:
        """Track a pending request with correlation ID."""
        timeout = timeout_override or self.timeout_seconds
        expires_at = datetime.utcnow() + timedelta(seconds=timeout)
        
        self.pending_requests[correlation_id] = {
            "request_data": request_data,
            "created_at": datetime.utcnow(),
            "expires_at": expires_at,
            "timeout_seconds": timeout
        }
        
        self.logger.debug(f"Tracking request with correlation ID: {correlation_id}")

    def get_request(self, correlation_id: str) -> Optional[Dict[str, Any]]:
        """Get tracked request by correlation ID."""
        request_info = self.pending_requests.get(correlation_id)
        
        if request_info:
            # Check if request has expired
            if datetime.utcnow() > request_info["expires_at"]:
                self.logger.warning(f"Request {correlation_id} has expired")
                self.remove_request(correlation_id)
                return None
            
            return request_info
        
        return None

    def remove_request(self, correlation_id: str) -> bool:
        """Remove tracked request by correlation ID."""
        if correlation_id in self.pending_requests:
            del self.pending_requests[correlation_id]
            self.logger.debug(f"Removed tracking for correlation ID: {correlation_id}")
            return True
        return False

    def cleanup_expired_requests(self) -> int:
        """Clean up expired requests and return count of removed requests."""
        current_time = datetime.utcnow()
        expired_ids = []
        
        for correlation_id, request_info in self.pending_requests.items():
            if current_time > request_info["expires_at"]:
                expired_ids.append(correlation_id)
        
        for correlation_id in expired_ids:
            self.remove_request(correlation_id)
        
        if expired_ids:
            self.logger.info(f"Cleaned up {len(expired_ids)} expired requests")
        
        return len(expired_ids)

    def get_stats(self) -> Dict[str, Any]:
        """Get correlation tracker statistics."""
        current_time = datetime.utcnow()
        active_count = 0
        expired_count = 0
        
        for request_info in self.pending_requests.values():
            if current_time <= request_info["expires_at"]:
                active_count += 1
            else:
                expired_count += 1
        
        return {
            "total_tracked": len(self.pending_requests),
            "active_requests": active_count,
            "expired_requests": expired_count,
            "timeout_seconds": self.timeout_seconds
        }
