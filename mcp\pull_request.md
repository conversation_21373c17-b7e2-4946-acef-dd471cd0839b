# Output Schema Support with Semantic Metadata Enhancement

<!-- Provide a brief summary of your changes -->
This PR adds automatic JSON Schema generation for tool return types with intelligent semantic metadata enhancement, enabling LLMs and client applications to understand both the structure and semantic meaning of data they'll receive from tools.

## Motivation and Context
<!-- Why is this change needed? What problem does it solve? -->
- Previously, tools only had input schemas but no structured metadata about their outputs
- This helps LLMs better understand the structure of data they'll receive from tools
- Particularly valuable for complex return types like Pydantic models and nested structures
- Improves the developer experience by making tools more self-documenting
- **NEW**: Adds semantic metadata enhancement that automatically detects semantic meaning from field names
- **NEW**: Enables client applications to provide intelligent UI rendering and formatting based on semantic types
- **NEW**: Supports 13+ semantic types including URLs, emails, datetime fields, media formats, currencies, and more

## Implementation Details

### Core Schema Enhancement System

- **New Module**: `python-sdk/src/mcp/server/fastmcp/utilities/schema.py`
  - `detect_semantic_format()`: Analyzes field names and types to detect semantic meaning
  - `enhance_output_schema()`: Embeds semantic metadata within JSON Schema properties
  - Maintains full JSON Schema compliance while adding optional semantic information

### Supported Semantic Types

- **Communication**: `email`, `url` (including uri, link, href variations)
- **DateTime**: `datetime` with subtypes (`date_only`, `time_only`, `datetime`)
- **Media**: `audio`, `video`, `image` with format detection (`audio_file`, `video_file`, `image_file`)
- **System**: `file_path`, `identifier` (id, uuid, guid), `status`, `color`
- **Numeric**: `currency`, `percentage` (validated for numeric types only)

### Automatic Integration

- **Tool Creation**: Integrated into `tools/base.py` via `Tool.from_function()`
- **Zero Configuration**: Works automatically with existing tools
- **Backward Compatible**: All existing functionality preserved

## How Has This Been Tested?
<!-- Have you tested this in a real application? Which scenarios were tested? -->

### Comprehensive Test Suite (29 Total Tests)

- **Unit Tests** (`test_schema_utilities.py`): 21 tests covering all semantic detection scenarios
  - Semantic type detection with positive/negative cases
  - Schema structure preservation and edge case handling
  - Media format detection, datetime subtyping, numeric validation
- **Integration Tests** (`test_tool_manager.py`): 8 tests in `TestOutputSchema` class
  - 3 new tests for advanced semantic enhancement scenarios
  - 5 updated existing tests to verify semantic enhancement while maintaining backward compatibility
- **End-to-End Testing**: Verified integration with FastMCP tool listing
- **Example Application**: Tested with enhanced output_schema_demo.py

## Schema Enhancement Examples

### Before and After Transformations

```json
// Basic field enhancement
// Before
{"type": "string", "title": "Email"}

// After
{"type": "string", "title": "Email", "semantic_type": "email"}

// DateTime with subtyping
// Before
{"type": "string", "title": "Created Date"}

// After
{"type": "string", "title": "Created Date", "semantic_type": "datetime", "datetime_type": "date_only"}

// Media with format detection
// Before
{"type": "string", "title": "Audio Mp3"}

// After
{"type": "string", "title": "Audio Mp3", "semantic_type": "audio", "media_format": "audio_file"}

// Numeric semantic types
// Before
{"type": "number", "title": "Account Balance"}

// After
{"type": "number", "title": "Account Balance", "semantic_type": "currency"}
```

### Client Application Benefits

- **Smart UI Rendering**: Email fields can show mail icons, URLs become clickable links
- **Format Validation**: Date fields get date pickers, currency fields show proper formatting
- **Media Handling**: Audio/video fields can show appropriate players or thumbnails
- **Enhanced UX**: Status fields can show colored indicators, percentages get progress bars

## Breaking Changes
<!-- Will users need to update their code or configurations? -->
- **Fully Non-Breaking**: Adds functionality without modifying existing behavior
- **Automatic Enhancement**: Existing tools automatically gain semantic metadata
- **Zero Migration**: No changes required to existing code
- **Backward Compatible**: All original schema properties preserved

## Types of changes
<!-- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [x] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)
- [x] Documentation update

## Checklist
<!-- Go over all the following points, and put an `x` in all the boxes that apply. -->
- [x] I have read the [MCP Documentation](https://modelcontextprotocol.io)
- [x] My code follows the repository's style guidelines
- [x] New and existing tests pass locally
- [x] I have added appropriate error handling
- [x] I have added or updated documentation as needed

## Additional Context and Implementation Notes

### Core Architecture

- **Type Extraction**: Uses Python's type annotations and inspect module to extract return type information
- **Schema Generation**: Maps primitive types directly to JSON Schema, leverages Pydantic for complex models
- **Enhancement Pipeline**: Automatically applies semantic detection during tool registration
- **Graceful Fallback**: Handles cases where schema generation isn't possible, falling back to None

### Design Decisions

- **Embedded Metadata**: Semantic information is embedded directly within JSON Schema properties rather than separate top-level fields, maintaining JSON Schema compliance
- **Case-Insensitive Detection**: Field name pattern matching works regardless of case (email, EMAIL, Email)
- **Type-Specific Validation**: Currency and percentage detection only applies to numeric types
- **Required Field Removal**: Output schemas automatically remove 'required' fields since tools always return complete objects
- **Extensible Architecture**: New semantic types can be easily added to the detection system

### Performance Considerations

- **Minimal Overhead**: Schema enhancement occurs only during tool registration, not during execution
- **Caching**: Enhanced schemas are cached as part of the tool definition
- **Lazy Evaluation**: Enhancement only runs when output schemas are successfully generated

### Future Extensibility

- **Plugin Architecture**: Detection patterns can be extended for domain-specific semantic types
- **Configuration Options**: Future versions could allow customization of detection rules
- **Nested Enhancement**: Current implementation focuses on top-level properties, with potential for nested object enhancement

Fixes #754
