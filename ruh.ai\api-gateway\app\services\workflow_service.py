from fastapi import HTTPException
import grpc
from typing import List, Dict, Any, Optional
from app.core.config import settings
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from google.protobuf.field_mask_pb2 import FieldMask
import json


class WorkflowServiceClient:
    """
    Client for interacting with the Workflow gRPC service.
    """

    def __init__(self):
        """
        Initializes the WorkflowServiceClient with a gRPC channel.
        """
        self.channel = grpc.insecure_channel(
            f"{settings.WORKFLOW_SERVICE_HOST}:{settings.WORKFLOW_SERVICE_PORT}"
        )
        self.stub = workflow_pb2_grpc.WorkflowServiceStub(self.channel)

    def _create_owner_proto(self, owner_details: dict) -> workflow_pb2.Owner:
        if not owner_details or "id" not in owner_details or not owner_details.get("id"):
            raise ValueError("Owner ID is missing or empty")
        return workflow_pb2.Owner(
            id=owner_details.get("id"),
            # email=owner_details.get("email", ""),
            # full_name=owner_details.get("full_name", ""),
            # fcm_token=owner_details.get("fcm_token", ""),
        )

    def _get_owner_type_enum(self, owner_type: str) -> workflow_pb2.WorkflowOwnerType:
        return {
            "user": workflow_pb2.WorkflowOwnerType.USER,
            "enterprise": workflow_pb2.WorkflowOwnerType.ENTERPRISE,
            "platform": workflow_pb2.WorkflowOwnerType.PLATFORM,
        }.get(owner_type.lower(), workflow_pb2.WorkflowOwnerType.USER)

    def _get_visibility_enum(self, visibility: str) -> workflow_pb2.WorkflowVisibility:
        return {
            "private": workflow_pb2.WorkflowVisibility.PRIVATE,
            "public": workflow_pb2.WorkflowVisibility.PUBLIC,
        }.get(visibility.lower(), workflow_pb2.WorkflowVisibility.PRIVATE)

    def _get_status_enum(self, status: str) -> workflow_pb2.WorkflowStatus:
        return {
            "active": workflow_pb2.WorkflowStatus.ACTIVE,
            "inactive": workflow_pb2.WorkflowStatus.INACTIVE,
        }.get(status.lower(), workflow_pb2.WorkflowStatus.ACTIVE)

    def _get_category_enum(self, category: str) -> workflow_pb2.WorkflowCategory:
        return {
            "automation": workflow_pb2.WorkflowCategory.AUTOMATION,
            "data_pipeline": workflow_pb2.WorkflowCategory.DATA_PIPELINE,
            "integration": workflow_pb2.WorkflowCategory.INTEGRATION,
            "web_scraping": workflow_pb2.WorkflowCategory.WEB_SCRAPING,
            "api": workflow_pb2.WorkflowCategory.API,
            "email": workflow_pb2.WorkflowCategory.EMAIL,
            "llm_orchestration": workflow_pb2.WorkflowCategory.LLM_ORCHESTRATION,
            "database": workflow_pb2.WorkflowCategory.DATABASE,
            "file_management": workflow_pb2.WorkflowCategory.FILE_MANAGEMENT,
            "scheduling": workflow_pb2.WorkflowCategory.SCHEDULING,
            "monitoring": workflow_pb2.WorkflowCategory.MONITORING,
            "crm": workflow_pb2.WorkflowCategory.CRM,
            "notifications": workflow_pb2.WorkflowCategory.NOTIFICATIONS,
            "document_processing": workflow_pb2.WorkflowCategory.DOCUMENT_PROCESSING,
            "devops": workflow_pb2.WorkflowCategory.DEVOPS,
            "general": workflow_pb2.WorkflowCategory.GENERAL,
        }.get(category.lower(), workflow_pb2.WorkflowCategory.GENERAL)


    async def create_workflow(
        self,
        name: str,
        workflow_data: Dict[str, Any],  # Accept Dict[str, Any]
        start_nodes: List[Dict[str, Any]],
        owner_type: str,
        owner_details: dict,
    ) -> workflow_pb2.CreateWorkflowResponse:
        """
        Creates a new workflow.

        :param name: The name of the workflow.
        :param description: A brief description of the workflow.
        :param workflow_data: The workflow definition.
        :param user_ids: List of user IDs associated with the workflow.
        :param owner_type: The type of owner for the workflow.
        :param visibility: The visibility status of the workflow.
        :param category: The category of the workflow.
        :param tags: Tags associated with the workflow.
        :param status: The status of the workflow.
        :param owner_details (dict): The user details of the agent owner.
        :return: A gRPC CreateWorkflowResponse object.
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = workflow_pb2.CreateWorkflowRequest(
            name=name,
            workflow_data=json.dumps(workflow_data),
            start_nodes=[json.dumps(node) for node in start_nodes],
            owner=owner,
            owner_type=self._get_owner_type_enum(owner_type),
        )

            print(f"[DEBUG] Sending gRPC request: {request}")
            response = self.stub.createWorkflow(request)
            print(f"[DEBUG] Workflow response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in create_workflow: {str(e)}")
            raise self._handle_error(e)

    async def get_workflow(
        self, workflow_id: str, user_id: Optional[str] = None
    ) -> workflow_pb2.WorkflowResponse:
        """
        Retrieves a workflow by its ID.

        :param workflow_id: The unique identifier of the workflow.
        :return: A gRPC WorkflowResponse object.
        """
        request = workflow_pb2.GetWorkflowRequest(id=workflow_id, user_id=user_id)
        try:
            response = self.stub.getWorkflow(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_workflow(
        self, workflow_id: str, owner_details: dict
    ) -> workflow_pb2.DeleteWorkflowResponse:
        """
        Deletes a workflow by its ID.

        :param workflow_id: The unique identifier of the workflow to delete.
        :return: A gRPC DeleteWorkflowResponse object.
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = workflow_pb2.DeleteWorkflowRequest(id=workflow_id, owner=owner)

            response = self.stub.deleteWorkflow(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_workflows(
        self,
        page: int,
        page_size: int,
        category: Optional[str] = None,
        status: Optional[str] = None,
        visibility: Optional[str] = None,
        search: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        user_id: Optional[str] = None,
    ) -> workflow_pb2.ListWorkflowsResponse:
        """
        Lists workflows with pagination and optional filters.

        Args:
            page: The page number to retrieve.
            page_size: The number of workflows per page.
            category: Optional filter by workflow category.
            status: Optional filter by workflow status.
            visibility: Optional filter by workflow visibility.
            search: Optional search term to filter workflows by name or description.
            tags: Optional dictionary of tag key-value pairs to filter workflows by.

        Returns:
            A gRPC ListWorkflowsResponse object.
        """
        try:
            request = workflow_pb2.ListWorkflowsRequest(page=page, page_size=page_size)

            # Add filters if provided
            if category:
                request.category = self._get_category_enum(category)

            if status:
                request.status = self._get_status_enum(status)

            if visibility:
                request.visibility = self._get_visibility_enum(visibility)

            if search:
                request.search = search

            if tags:
                request.tags = json.dumps(tags)

            if user_id:
                request.user_id = user_id

            print(
                f"[DEBUG] Listing workflows with filters: category={category}, status={status}, "
                f"visibility={visibility}, search={search}, tags={tags}"
            )
            return self.stub.listWorkflows(request)
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in list_workflows: {str(e)}")
            raise self._handle_error(e)

    async def update_workflow_details(
        self, workflow_id: str, update_fields: dict, owner_details: dict
    ):
        """
        Updates specific details of a workflow.

        This method allows updating workflow metadata like name, description, tags, etc.
        All fields are optional - only the provided fields will be updated.

        Args:
            workflow_id: The ID of the workflow to update
            update_fields: Dictionary of fields to update (all optional)
            owner_details: Details of the user making the request (for authorization)

        Returns:
            The gRPC response containing success status and message

        Raises:
            Exception: If there's an error communicating with the workflow service
        """
        try:
            print(
                f"[DEBUG] gRPC Client: Updating workflow details for ID: {workflow_id} with fields: {list(update_fields.keys())}"
            )

            request_args = {"id": workflow_id}
            proto_field_paths = []

            # Add owner to request for authorization purposes
            request_args["owner"] = self._create_owner_proto(owner_details)

            # Map fields from update_fields to request_args and track which fields to update
            if "name" in update_fields:
                request_args["name"] = update_fields["name"]
                proto_field_paths.append("name")
            if "description" in update_fields:
                request_args["description"] = update_fields["description"]
                proto_field_paths.append("description")
            if "tags" in update_fields and update_fields["tags"] is not None:
                request_args["tags"] = json.dumps(update_fields["tags"])
                proto_field_paths.append("tags")
            if "visibility" in update_fields and update_fields["visibility"] is not None:
                request_args["visibility"] = self._get_visibility_enum(update_fields["visibility"])
                proto_field_paths.append("visibility")
            if "category" in update_fields and update_fields["category"] is not None:
                request_args["category"] = self._get_category_enum(update_fields["category"])
                proto_field_paths.append("category")
            if "status" in update_fields and update_fields["status"] is not None:
                request_args["status"] = self._get_status_enum(update_fields["status"])
                proto_field_paths.append("status")

            request_args["update_mask"] = FieldMask(paths=proto_field_paths)


            if not proto_field_paths:
                raise ValueError("No fields provided for update in PATCH request.")

            request = workflow_pb2.UpdateWorkflowRequest(**request_args)


            print(f"[DEBUG] gRPC Client: Sending UpdateWorkflowRequest: {request}")
            response = self.stub.updateWorkflow(request)
            print(f"[DEBUG] gRPC Client: Received response: {response}")
            return response


        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in update_workflow_details: {e.details()}")
            raise self._handle_error(e)

    async def list_workflows_by_user_id(
        self,
        owner_id: str,
        page: int,
        page_size: int,
        category: Optional[str] = None,
        status: Optional[str] = None,
        visibility: Optional[str] = None,
    ):
        """
        Retrieves workflows by user ID with pagination.

        :param owner_id: The unique identifier of the workflow owner.
        :param page: The page number to retrieve.
        :param page_size: The number of workflows per page.
        :return: A gRPC ListWorkflowsByUserIdResponse object.
        """
        request = workflow_pb2.ListWorkflowsByUserIdRequest(
            owner_id=owner_id, page=page, page_size=page_size
        )
        # Add filters if provided
        if category:
            request.category = self._get_category_enum(category)

        if status:
            request.status = self._get_status_enum(status)

        if visibility:
            request.visibility = self._get_visibility_enum(visibility)

        try:
            return self.stub.listWorkflowsByUserId(request)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_workflows_by_ids(self, workflow_ids: List[str]):
        """
        Gets multiple workflows by their IDs from the gRPC service.
        """
        try:
            print(f"[DEBUG] gRPC Client: Requesting workflows with IDs: {workflow_ids}")
            request_proto = workflow_pb2.GetWorkflowsByIdsRequest(ids=workflow_ids)

            response = self.stub.getWorkflowsByIds(request_proto)  # If sync stub
            print(f"[DEBUG] gRPC Client: Received response: {response}")
            print(
                f"[DEBUG] gRPC Client: Received {len(response.workflows)} workflows for {len(workflow_ids)} requested IDs."
            )
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_workflows_by_ids: {e.details()}")
            raise self._handle_error(e)

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")

    # Add these template-related methods to your existing WorkflowServiceClient class

    async def create_template(
        self,
        name: str,
        description: str,
        workflow_data: Dict[str, Any],
        owner_details: dict,
        category: Optional[str] = None,
        tags: Optional[Dict[str, Any]] = None,
        status: str = "active",
        start_node_data: Optional[List[str]] = None,
    ) -> workflow_pb2.CreateTemplateResponse:
        """
        Creates a new workflow template.

        Args:
            name: The name of the template
            description: A description of the template
            workflow_data: The workflow definition data
            visibility: Template visibility setting
            category: Optional category
            tags: Optional tags as key-value pairs
            version: Template version
            status: Template status

        Returns:
            The gRPC CreateTemplateResponse
        """
        try:
            request = workflow_pb2.CreateTemplateRequest(
                name=name,
                description=description,
                workflow_data=json.dumps(workflow_data),
                owner=self._create_owner_proto(owner_details),
                category=self._get_category_enum(category),
                tags=json.dumps(tags) if tags is not None else "",
                status=self._get_status_enum(status),
                start_nodes=start_node_data if start_node_data else [],
            )
            print(f"[DEBUG] Sending template creation request: {request}")
            response = self.stub.createWorkflowTemplate(request)
            print(f"[DEBUG] Template creation response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in create_template: {str(e)}")
            raise self._handle_error(e)

    async def get_template(self, template_id: str) -> workflow_pb2.GetTemplateResponse:
        """
        Retrieves a workflow template by its ID.

        Args:
            template_id: The ID of the template to retrieve

        Returns:
            The gRPC GetTemplateResponse
        """
        request = workflow_pb2.GetTemplateRequest(id=template_id)
        try:
            print(f"[DEBUG] Getting template with ID: {template_id}")
            response = self.stub.getTemplate(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in get_template: {str(e)}")
            raise self._handle_error(e)

    async def list_templates(
        self,
        page: int = 1,
        page_size: int = 10,
        category: Optional[str] = None,
        search: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ):
        # ) -> workflow_pb2.ListTemplatesResponse:
        """
        Lists workflow templates with optional filtering.

        This method retrieves a paginated list of workflow templates from the workflow service,
        with support for various filtering options.

        Args:
            page: Page number for pagination (starts at 1)
            page_size: Number of items per page (between 1 and 100)
            category: Optional filter by workflow category (e.g., 'WORKFLOW_CATEGORY_GENERAL')
            status: Optional filter by template status (e.g., 'STATUS_ACTIVE')
            search: Optional search term to filter templates by name or description
            tags: Optional dictionary of tag key-value pairs to filter templates by

        Returns:
            A ListTemplatesResponse containing the matching templates and pagination metadata

        Raises:
            Exception: If there's an error communicating with the workflow service
        """
        try:
            request = workflow_pb2.ListTemplatesRequest(page=page, page_size=page_size)

            if category:
                request.category = category
            if search:
                request.search = search
            if tags:
                request.tags = json.dumps(tags)

            request.status = workflow_pb2.WorkflowStatus.ACTIVE
            request.visibility = workflow_pb2.WorkflowVisibility.PUBLIC

            print(f"[DEBUG] Listing templates with request: {request}")
            response = self.stub.listTemplates(request)

            print(f"[DEBUG] Templates response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in list_templates: {str(e)}")
            raise self._handle_error(e)

    async def patch_template(
        self,
        template_id: str,
        update_fields: dict,
        owner_details: dict,
    ):
        try:
            print(
                f"[DEBUG] gRPC Client: Patching template ID: {template_id} with fields: {list(update_fields.keys())}"
            )

            request_args = {"id": template_id}
            proto_field_paths = []

            # Add owner to request for authorization purposes only
            # This is not for changing ownership, just for verifying the requester
            request_args["owner"] = self._create_owner_proto(owner_details)

            if "name" in update_fields:
                request_args["name"] = update_fields["name"]
                proto_field_paths.append("name")
            if "description" in update_fields:
                request_args["description"] = update_fields["description"]
                proto_field_paths.append("description")
            if "workflow_data" in update_fields and update_fields["workflow_data"] is not None:
                request_args["workflow_data"] = json.dumps(update_fields["workflow_data"])
                proto_field_paths.append("workflow_data")
            if "start_node_data" in update_fields and update_fields["start_node_data"] is not None:
                request_args["start_nodes"] = update_fields[
                    "start_node_data"
                ]  # maps to 'start_nodes'
                proto_field_paths.append("start_nodes")
            if "category" in update_fields and update_fields["category"] is not None:
                request_args["category"] = self._get_category_enum(update_fields["category"])
                proto_field_paths.append("category")
            if "tags" in update_fields and update_fields["tags"] is not None:
                request_args["tags"] = json.dumps(update_fields["tags"])
                proto_field_paths.append("tags")
            if "version" in update_fields and update_fields["version"] is not None:
                request_args["version"] = update_fields["version"]
                proto_field_paths.append("version")
            if "status" in update_fields and update_fields["status"] is not None:
                request_args["status"] = self._get_status_enum(update_fields["status"])
                proto_field_paths.append("status")

            if not proto_field_paths:
                raise ValueError("No fields provided for template update in PATCH request.")

            request_args["update_mask"] = FieldMask(paths=proto_field_paths)
            request = workflow_pb2.UpdateTemplateRequest(**request_args)

            print(f"[DEBUG] gRPC Client: Sending UpdateTemplateRequest: {request}")
            # response = await self.stub.updateTemplate(request) # Async stub
            response = self.stub.updateTemplate(request)  # Sync stub
            print(f"[DEBUG] gRPC Client: Received UpdateTemplateResponse: {response}")
            return response

        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in patch_template: {e.details()}")
            # raise self._handle_error(e)
            raise Exception(f"gRPC Error: {e.details()}")
        except ValueError as ve:
            print(f"[ERROR] gRPC Client: ValueError in patch_template: {ve}")
            raise

    async def delete_template(self, template_id: str) -> workflow_pb2.DeleteTemplateResponse:
        """
        Deletes a workflow template.

        Args:
            template_id: ID of the template to delete

        Returns:
            The gRPC DeleteTemplateResponse
        """
        try:
            request = workflow_pb2.DeleteTemplateRequest(id=template_id)
            print(f"[DEBUG] Deleting template with ID: {template_id}")
            response = self.stub.deleteTemplate(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in delete_template: {str(e)}")
            raise self._handle_error(e)

    async def create_workflow_from_template(
        self, template_id: str, owner_type: str, owner_details: dict
    ) -> workflow_pb2.CreateWorkflowFromTemplateResponse:
        """
        Creates a new workflow from a template.

        Args:
            template_id: ID of the template to use
            owner_type: Type of owner for the new workflow
            owner_details: Details of the workflow owner

        Returns:
            gRPC response with success status and message
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = workflow_pb2.CreateWorkflowFromTemplateRequest(
                template_id=template_id,
                owner=owner,
                owner_type=self._get_owner_type_enum(owner_type),
            )
            print(f"[DEBUG] Creating workflow from template ID: {template_id}")
            return self.stub.createWorkflowFromTemplate(request)
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in create_workflow_from_template: {str(e)}")
            raise self._handle_error(e)

    async def toggle_workflow_visibility(
        self, workflow_id: str, owner_details: dict
    ) -> workflow_pb2.ToggleWorkflowVisibilityResponse:
        """
        Toggles the visibility of a workflow between PUBLIC and PRIVATE.

        This method sends a request to the workflow service to toggle the visibility
        of a workflow. If the workflow is currently PUBLIC, it will be set to PRIVATE,
        and vice versa.

        Args:
            workflow_id: The ID of the workflow to toggle visibility for
            owner_details: Details of the user making the request (for authorization)

        Returns:
            The gRPC response containing success status, message, and updated workflow

        Raises:
            Exception: If there's an error communicating with the workflow service
        """
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request = workflow_pb2.ToggleWorkflowVisibilityRequest(
                workflow_id=workflow_id, owner=owner_proto
            )
            print(
                f"[DEBUG] gRPC Client: Sending ToggleWorkflowVisibilityRequest for workflow_id: {workflow_id}"
            )
            response = self.stub.toggleWorkflowVisibility(request)
            print(f"[DEBUG] gRPC Client: Received ToggleWorkflowVisibilityResponse: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in toggle_workflow_visibility: {e.details()}")
            raise self._handle_error(e)

    async def patch_workflow(
        self,
        workflow_id: str,
        update_fields: dict,  # This comes from WorkflowPatchPayload.model_dump(exclude_unset=True)
        owner_details: dict,
    ):
        """
        Updates specific fields of a workflow.

        This method allows updating any aspect of a workflow with a partial update.
        All fields are optional - only the provided fields will be updated.

        Args:
            workflow_id: The ID of the workflow to update
            update_fields: Dictionary of fields to update (all optional)
            owner_details: Details of the user making the request (for authorization)

        Returns:
            The gRPC response containing success status and message

        Raises:
            Exception: If there's an error communicating with the workflow service
        """
        try:
            request_args = {"id": workflow_id}
            proto_field_paths = []
            request_args["owner"] = self._create_owner_proto(owner_details)

            # Map Pydantic/API field names to proto field names and handle types
            if "name" in update_fields:
                request_args["name"] = update_fields["name"]
                proto_field_paths.append("name")
            if "description" in update_fields:
                request_args["description"] = update_fields["description"]
                proto_field_paths.append("description")
            if "workflow_data" in update_fields and update_fields["workflow_data"] is not None:
                request_args["workflow_data"] = json.dumps(update_fields["workflow_data"])
                proto_field_paths.append("workflow_data")
            if (
                "start_node_data" in update_fields and update_fields["start_node_data"] is not None
            ):  # Pydantic field name
                request_args["start_nodes"] = [json.dumps(node) for node in update_fields["start_node_data"]]
                proto_field_paths.append("start_nodes")
            if "user_ids" in update_fields:  # Allow setting to None/empty
                request_args["user_ids"] = (
                    update_fields["user_ids"] if update_fields["user_ids"] is not None else []
                )
                proto_field_paths.append("user_ids")
            if "category" in update_fields and update_fields["category"] is not None:
                request_args["category"] = self._get_category_enum(update_fields["category"])
                proto_field_paths.append("category")
            if "tags" in update_fields:  # Allow setting to None/empty
                request_args["tags"] = (
                    json.dumps(update_fields["tags"]) if update_fields["tags"] is not None else "{}"
                )
                proto_field_paths.append("tags")
            if "status" in update_fields and update_fields["status"] is not None:
                request_args["status"] = self._get_status_enum(update_fields["status"])
                proto_field_paths.append("status")
            if "version" in update_fields:
                request_args["version"] = update_fields["version"]
                proto_field_paths.append("version")
            if (
                "is_changes_marketplace" in update_fields
                and update_fields["is_changes_marketplace"] is not None
            ):
                request_args["is_changes_marketplace"] = update_fields["is_changes_marketplace"]
                proto_field_paths.append("is_changes_marketplace")

            if not proto_field_paths:
                raise ValueError("No fields provided for update in PATCH request.")

            request_args["update_mask"] = FieldMask(paths=proto_field_paths)
            request = workflow_pb2.UpdateWorkflowRequest(**request_args)

            print(f"[DEBUG] gRPC Client: Sending UpdateWorkflowRequest: {request}")
            response = self.stub.updateWorkflow(request)
            print(f"[DEBUG] gRPC Client: Received response from UpdateWorkflow: {response}")
            return response

        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in patch_workflow: {e.details()}")
            raise self._handle_error(e)  # Or a custom exception
        except ValueError as ve:  # From the "No fields provided" check
            print(f"[ERROR] gRPC Client: ValueError in patch_workflow: {ve}")
            raise
        except Exception as ex:
            print(f"[ERROR] gRPC Client: Exception in patch_workflow: {str(ex)}")
            raise Exception(f"gRPC Client Error: {str(ex)}")

    async def update_workflow_settings(
        self, workflow_id: str, owner_details: dict, settings_payload: dict
    ):
        try:
            owner_proto = self._create_owner_proto(owner_details)
            request_args = {
                "workflow_id": workflow_id,
                "owner": owner_proto,
            }
            # Only include fields if they are in settings_payload and not None
            if (
                "is_changes_marketplace" in settings_payload
                and settings_payload["is_changes_marketplace"] is not None
            ):
                request_args["is_changes_marketplace"] = settings_payload["is_changes_marketplace"]

            if "status" in settings_payload and settings_payload["status"] is not None:
                request_args["status"] = self._get_status_enum(settings_payload["status"])

            request = workflow_pb2.UpdateWorkflowSettingsRequest(**request_args)
            # ... (send request and return response) ...
            response = self.stub.updateWorkflowSettings(request)
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in patch_workflow: {e.details()}")
            raise self._handle_error(e)  # Or a custom exception

    async def list_templates_by_user_id(
        self, owner_id: str, page: int, page_size: int, category: Optional[str] = None
    ):
        try:
            request = workflow_pb2.ListTemplatesByUserIdRequest(
                owner_id=owner_id, page=page, page_size=page_size
            )
            # Add filters if provided
            if category:
                request.category = self._get_category_enum(category)


            print(f"[DEBUG] Listing templates with filters: category={category}")
            return self.stub.listTemplatesByUserId(request)
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in list_templates_by_user_id: {str(e)}")
            raise self._handle_error(e)

    async def rate_workflow(
        self, workflow_id: str, user_id: str, rating: float
    ) -> workflow_pb2.RateWorkflowResponse:
        """
        Rate a workflow with a score from 1 to 5.

        Args:
            workflow_id: The ID of the workflow to rate
            user_id: The ID of the user providing the rating
            rating: Rating value between 1.0 and 5.0

        Returns:
            The gRPC RateWorkflowResponse
        """
        try:
            request = workflow_pb2.RateWorkflowRequest(
                workflow_id=workflow_id, user_id=user_id, rating=rating
            )
            print(f"[DEBUG] Sending rate workflow request: {request}")
            response = self.stub.rateWorkflow(request)
            print(f"[DEBUG] Rate workflow response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in rate_workflow: {str(e)}")
            raise self._handle_error(e)

    async def use_workflow(
        self, workflow_id: str, user_id: str
    ) -> workflow_pb2.UseWorkflowResponse:
        """
        Mark a workflow as used and increment its usage count.

        Args:
            workflow_id: The ID of the workflow to use
            user_id: The ID of the user using the workflow

        Returns:
            The gRPC UseWorkflowResponse
        """
        try:
            request = workflow_pb2.UseWorkflowRequest(workflow_id=workflow_id, user_id=user_id)
            print(f"[DEBUG] Sending use workflow request: {request}")
            response = self.stub.useWorkflow(request)
            print(f"[DEBUG] Use workflow response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in use_workflow: {str(e)}")
            raise self._handle_error(e)

    async def add_workflow(
        self,
        name: str,
        workflow_data: Dict[str, Any],
        owner_details: dict,
        start_nodes: List[str],
        description: str = "",
        owner_type: str = "user",
        visibility: str = "private",
        category: Optional[str] = None,
        tags: Optional[Dict[str, Any]] = None,
        status: str = "active",
    ) -> workflow_pb2.CreateWorkflowResponse:
        """
        Add a new workflow.

        Args:
            name: The name of the workflow
            workflow_data: The workflow definition data
            owner_details: Details of the owner
            start_nodes: List of start node IDs
            description: Optional description
            owner_type: Type of owner (user/organization)
            visibility: Visibility setting (public/private)
            category: Optional category
            tags: Optional tags as key-value pairs
            status: Workflow status

        Returns:
            The gRPC CreateWorkflowResponse
        """
        try:
            owner = self._create_owner_proto(owner_details)
            request = workflow_pb2.CreateWorkflowRequest(
                name=name,
                description=description,
                workflow_data=json.dumps(workflow_data),
                start_nodes=start_nodes,
                visibility=self._get_visibility_enum(visibility),
                owner=owner,
                owner_type=self._get_owner_type_enum(owner_type),
                category=self._get_category_enum(category) if category else None,
                tags=json.dumps(tags) if tags else "",
                status=self._get_status_enum(status),
            )
            print(f"[DEBUG] Sending add workflow request: {request}")
            response = self.stub.createWorkflow(request)
            print(f"[DEBUG] Add workflow response: {response}")
            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in add_workflow: {str(e)}")
            raise self._handle_error(e)

    async def get_marketplace_workflows(
        self,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        sort_by: Optional[str] = None,
    ) -> Any:
        """
        Retrieves a paginated list of public workflows for the marketplace.

        Args:
            page: Page number for pagination
            page_size: Number of items per page
            search: Optional search term to filter by name or description
            category: Optional category filter
            tags: Optional tags filter as a dictionary of key-value pairs
            sort_by: Optional sort criteria (NEWEST, OLDEST, MOST_POPULAR, HIGHEST_RATED)

        Returns:
            Response containing the list of marketplace workflows and pagination metadata
        """
        try:
            request_args = {
                "page": page,
                "page_size": page_size,
                "visibility": "WORKFLOW_VISIBILITY_PUBLIC",
            }

            if search:
                request_args["search"] = search
            if category:
                request_args["category"] = self._get_category_enum(category)
            if tags:
                request_args["tags"] = json.dumps(tags)
            if sort_by:
                request_args["sort_by"] = sort_by

            request = workflow_pb2.GetMarketplaceWorkflowsRequest(**request_args)

            print(f"[DEBUG] gRPC Client: Sending GetMarketplaceWorkflowsRequest: {request}")
            response = self.stub.getMarketplaceWorkflows(request)
            print(f"[DEBUG] gRPC Client: Received response: {response}")

            return response
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC Client: RpcError in get_marketplace_workflows: {e.details()}")
            raise self._handle_error(e)

    async def discover_components(self, force_refresh: bool = False):
        try:
            request = workflow_pb2.DiscoverComponentsRequest(force_refresh=force_refresh)
            print(f"[DEBUG] Discovering components with force_refresh={force_refresh}")
            return self.stub.discoverComponents(request)
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in discover_components: {str(e)}")
            raise self._handle_error(e)

    async def validate_workflow(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]], workflow_name: str = "Untitled Workflow", workflow_id: Optional[str] = None, execution_id: Optional[str] = None) -> workflow_pb2.ValidateWorkflowResponse:
        """
        Validates a workflow and returns any missing required fields.

        Args:
            nodes: List of nodes in the workflow
            edges: List of edges in the workflow
            workflow_name: Name of the workflow
            workflow_id: Optional ID of the workflow
            execution_id: Optional ID of the execution

        Returns:
            A gRPC ValidateWorkflowResponse object
        """
        try:
            # Convert nodes to RequestNode protos
            request_nodes = []
            for node in nodes:
                node_data = {}
                for key, value in node.get("data").items():
                    # Convert complex data types to strings
                    if isinstance(value, (dict, list)):
                        node_data[key] = json.dumps(value)
                    else:
                        node_data[key] = str(value) if value is not None else ""

                request_node = workflow_pb2.RequestNode(
                    id=node.get("id"),
                    type=node.get("type"),
                    data=node_data,
                    x=float(node.get("position").get("x", 0)),
                    y=float(node.get("position").get("y", 0))
                )
                request_nodes.append(request_node)

            # Convert edges to RequestEdge protos
            request_edges = []
            for edge in edges:
                request_edge = workflow_pb2.RequestEdge(
                    id=edge.get("id", ""),
                    source=edge.get("source", ""),
                    target=edge.get("target", ""),
                    sourceHandle=edge.get("sourceHandle", ""),
                    targetHandle=edge.get("targetHandle", "")
                )
                request_edges.append(request_edge)
            print(f"[DEBUG] Request nodes: {request_nodes}")
            print(f"[DEBUG] Request edges: {request_edges}")
            # Create the request
            request = workflow_pb2.ValidateWorkflowRequest(
                nodes=request_nodes,
                edges=request_edges,
                workflow_name=workflow_name
            )

            # Add optional fields if provided
            if workflow_id:
                request.workflow_id = workflow_id
            if execution_id:
                request.execution_id = execution_id

            print(f"[DEBUG] Validating workflow with {len(nodes)} nodes and {len(edges)} edges")
            return self.stub.validateWorkflow(request)
        except grpc.RpcError as e:
            print(f"[ERROR] gRPC error in validate_workflow: {str(e)}")
            raise self._handle_error(e)
