import express from 'express';
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import { OAuth2Client } from "google-auth-library";

// Import modular components
import { initializeOAuth2Client } from './auth/client.js';
import { AuthServer } from './auth/server.js';
import { TokenManager } from './auth/tokenManager.js';
import { getToolDefinitions } from './handlers/listTools.js';
import { handleCallTool } from './handlers/callTool.js';

// Global variables
let oauth2Client: OAuth2Client;
let tokenManager: TokenManager;
let authServer: AuthServer;
let mcpServer: Server;

// Session management for SSE
interface SSESession {
  id: string;
  createdAt: Date;
  lastEventId: number;
  isActive: boolean;
}

const activeSessions = new Map<string, SSESession>();
let globalEventId = 0;

// Simple logger for production
const logger = {
  info: (message: string, meta?: any) => {
    console.log(JSON.stringify({ level: 'info', message, meta, timestamp: new Date().toISOString() }));
  },
  error: (message: string, error?: any) => {
    console.error(JSON.stringify({ level: 'error', message, error: error?.message || error, timestamp: new Date().toISOString() }));
  },
  warn: (message: string, meta?: any) => {
    console.warn(JSON.stringify({ level: 'warn', message, meta, timestamp: new Date().toISOString() }));
  }
};

// Initialize MCP Server
async function initializeMCPServer() {
  // Create MCP server instance
  mcpServer = new Server(
    {
      name: "google-calendar",
      version: "1.0.0",
    },
    {
      capabilities: {
        tools: {},
      },
    }
  );

  // Initialize Authentication
  const isProduction = process.env.NODE_ENV === 'production';

  try {
    oauth2Client = await initializeOAuth2Client();
    tokenManager = new TokenManager(oauth2Client);
    authServer = new AuthServer(oauth2Client);

    // Start auth server if authentication is required
    // In production/Cloud Run, we might skip the interactive auth
    if (!isProduction) {
      const authSuccess = await authServer.start(false); // Don't open browser in Cloud Run
      if (!authSuccess) {
        console.warn("Authentication failed, but continuing in production mode");
      }
    } else {
      console.log("Running in production mode - skipping interactive authentication");
    }
  } catch (error) {
    if (isProduction) {
      console.warn("OAuth client initialization failed in production mode, creating minimal client:", error);
      // Create a minimal OAuth client for production that can be configured later
      oauth2Client = new OAuth2Client();
      tokenManager = new TokenManager(oauth2Client);
      authServer = new AuthServer(oauth2Client);
    } else {
      throw error;
    }
  }

  // Set up MCP Handlers
  mcpServer.setRequestHandler(ListToolsRequestSchema, async () => {
    return getToolDefinitions();
  });

  mcpServer.setRequestHandler(CallToolRequestSchema, async (request) => {
    const isProduction = process.env.NODE_ENV === 'production';
    if (!isProduction && !(await tokenManager.validateTokens())) {
      throw new Error("Authentication required");
    }
    return handleCallTool(request, oauth2Client);
  });
}

// Create HTTP server
async function createHTTPServer(): Promise<express.Application> {
  const app = express();
  const port = process.env.PORT || 3000;

  // Middleware
  app.use(express.json({ limit: '10mb' }));

  // Request logging middleware
  app.use((req, res, next) => {
    const start = Date.now();
    const requestId = req.headers['x-request-id'] || `req_${Date.now()}`;

    res.on('finish', () => {
      const duration = Date.now() - start;
      logger.info('HTTP Request', {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration,
        requestId,
        userAgent: req.headers['user-agent']
      });
    });

    next();
  });

  // CORS middleware for production
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Request-ID');
    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({ status: 'healthy', service: 'google-calendar-mcp' });
  });

  // MCP-compliant SSE endpoint (GET /sse)
  app.get('/sse', (req, res) => {
    // Validate Accept header
    const acceptHeader = req.headers.accept;
    if (!acceptHeader?.includes('text/event-stream')) {
      return res.status(406).json({
        error: 'Not Acceptable: Client must accept text/event-stream'
      });
    }

    // Get or create session
    const sessionId = req.headers['mcp-session-id'] as string ||
      `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const lastEventId = req.headers['last-event-id'] ?
      parseInt(req.headers['last-event-id'] as string, 10) : 0;

    // Create or get session
    let session = activeSessions.get(sessionId);
    if (!session) {
      session = {
        id: sessionId,
        createdAt: new Date(),
        lastEventId: 0,
        isActive: true
      };
      activeSessions.set(sessionId, session);
    }

    // Set SSE headers according to MCP spec
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'mcp-session-id': sessionId,
    });

    // Send initial connection event
    const eventId = ++globalEventId;
    session.lastEventId = eventId;

    const connectionEvent = {
      jsonrpc: '2.0',
      method: 'notifications/initialized',
      params: {}
    };

    res.write(`id: ${eventId}\n`);
    res.write(`data: ${JSON.stringify(connectionEvent)}\n\n`);

    // Keep connection alive with periodic pings
    const pingInterval = setInterval(() => {
      if (session?.isActive) {
        res.write(': ping\n\n');
      } else {
        clearInterval(pingInterval);
      }
    }, 30000);

    // Handle client disconnect
    req.on('close', () => {
      if (session) {
        session.isActive = false;
        activeSessions.delete(sessionId);
      }
      clearInterval(pingInterval);
      logger.info('SSE client disconnected', { sessionId });
    });

    logger.info('SSE connection established', { sessionId, lastEventId });
  });

  // Root endpoint
  app.get('/', (req, res) => {
    res.status(200).json({
      message: 'Google Calendar MCP Server',
      version: '1.0.0',
      protocol: 'MCP StreamableHTTP',
      endpoints: {
        health: '/health',
        sse: '/sse (GET - Server-Sent Events)',
        message: '/message (POST - JSON-RPC 2.0)',
        tools: '/tools (Legacy)',
      },
      activeSessions: activeSessions.size,
      capabilities: {
        tools: true,
        streaming: true,
        sessionManagement: true
      }
    });
  });

  // List tools endpoint
  app.get('/tools', async (req, res) => {
    try {
      const tools = getToolDefinitions();
      res.json(tools);
    } catch (error) {
      console.error('Error listing tools:', error);
      res.status(500).json({ error: 'Failed to list tools' });
    }
  });

  // MCP-compliant message endpoint (POST /message)
  app.post('/message', async (req, res) => {
    try {
      const sessionId = req.headers['mcp-session-id'] as string;
      const session = sessionId ? activeSessions.get(sessionId) : null;

      // Validate session for stateful mode
      if (sessionId && !session) {
        return res.status(400).json({
          jsonrpc: '2.0',
          error: {
            code: -32002,
            message: 'Invalid session ID'
          },
          id: req.body?.id || null
        });
      }

      // Validate JSON-RPC format
      if (!req.body || !req.body.jsonrpc || req.body.jsonrpc !== '2.0') {
        return res.status(400).json({
          jsonrpc: '2.0',
          error: {
            code: -32600,
            message: 'Invalid Request: Must be JSON-RPC 2.0'
          },
          id: req.body?.id || null
        });
      }

      const isProduction = process.env.NODE_ENV === 'production';

      // Authentication check
      if (!isProduction && !(await tokenManager.validateTokens())) {
        return res.status(401).json({
          jsonrpc: '2.0',
          error: {
            code: -32002,
            message: 'Authentication required'
          },
          id: req.body.id
        });
      }

      // Handle different MCP methods
      const { method, params, id } = req.body;

      let result;
      switch (method) {
        case 'tools/list':
          result = getToolDefinitions();
          break;

        case 'tools/call':
          if (!params?.name) {
            return res.status(400).json({
              jsonrpc: '2.0',
              error: {
                code: -32602,
                message: 'Invalid params: tool name required'
              },
              id
            });
          }
          result = await handleCallTool({ params }, oauth2Client);
          break;

        default:
          return res.status(400).json({
            jsonrpc: '2.0',
            error: {
              code: -32601,
              message: `Method not found: ${method}`
            },
            id
          });
      }

      // Send JSON-RPC response
      res.json({
        jsonrpc: '2.0',
        result,
        id
      });

    } catch (error) {
      logger.error('Error processing MCP message', error);

      const errorResponse = {
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: error instanceof Error ? error.message : 'Internal error'
        },
        id: req.body?.id || null
      };

      res.status(500).json(errorResponse);
    }
  });

  // Start server and return a promise that resolves when listening
  return new Promise((resolve, reject) => {
    const server = app.listen(port, () => {
      console.log(`Google Calendar MCP HTTP Server running on port ${port}`);
      resolve(app);
    });

    server.on('error', (error) => {
      console.error('Failed to start HTTP server:', error);
      reject(error);
    });
  });
}

// Main function
async function main() {
  try {
    console.log('Initializing Google Calendar MCP Server...');
    await initializeMCPServer();
    console.log('MCP Server initialized successfully');

    console.log('Starting HTTP server...');
    await createHTTPServer();

    // Set up graceful shutdown
    process.on("SIGINT", cleanup);
    process.on("SIGTERM", cleanup);

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Cleanup function
async function cleanup() {
  try {
    console.log('Shutting down server...');
    if (authServer) {
      await authServer.stop();
    }
    process.exit(0);
  } catch (error) {
    console.error('Error during cleanup:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url.startsWith('file://') && process.argv[1]?.endsWith('http-server.js')) {
  main().catch((error) => {
    console.error('Server startup failed:', error);
    process.exit(1);
  });
}

export { main, createHTTPServer };
