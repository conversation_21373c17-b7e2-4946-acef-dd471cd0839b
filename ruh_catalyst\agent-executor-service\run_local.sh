#!/bin/bash

# run_local.sh
# Local development script for Agent Executor Service

set -e

echo "Starting Agent Executor Service locally..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "Please update .env file with your configuration before running again."
    exit 1
fi

# Check if poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "Poetry is not installed. Please install poetry first:"
    echo "curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
poetry install

# Run the service
echo "Starting Agent Executor Service..."
poetry run python -m app.main
