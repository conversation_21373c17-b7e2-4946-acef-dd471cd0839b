# Agent Executor Service

## Overview

The Agent Executor Service is the centralized execution engine for all agents across the platform. Following the proven patterns from the mcp-executor-service, it handles agent instantiation, execution orchestration, session management, and resource monitoring while the agent platform focuses on configuration and management tasks.

## Phase 1 Implementation Status

This is the **Phase 1 Foundation** implementation that includes:

✅ **Completed:**

- Project structure following mcp-executor-service patterns
- Core configuration management with environment variables
- Basic Kafka service implementation for message processing
- Essential data models for agent execution requests/responses
- Basic health check endpoints and monitoring
- Core agent executor with placeholder execution logic
- Comprehensive test structure
- Docker containerization support
- Development environment setup

🚧 **Phase 2 (Weeks 3-4):** Agent Platform Integration

- Configuration retrieval from agent platform via Kafka
- Tool integration migration (MCP, Workflow, Dynamic)
- Session management with Redis

🚧 **Phase 3 (Weeks 5-6):** AutoGen Framework Integration

- AutoGen 0.5.7 team creation and execution
- Streaming response processing
- Advanced resource management

🚧 **Phase 4 (Weeks 7-8):** Production Readiness

- Comprehensive testing and validation
- Production deployment configuration
- Monitoring and alerting setup

## Quick Start

### Prerequisites

- Python 3.11+
- Poetry (for dependency management)
- Kafka (for message processing)
- Redis (for session management - Phase 2)

### Local Development

1. **Clone and Setup**

   ```bash
   cd ruh_catalyst/agent-executor-service
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Install Dependencies**

   ```bash
   # For development with Poetry
   poetry install

   # Or install directly with pip
   pip install aiokafka aiohttp psutil pydantic pydantic-settings
   ```

3. **Verify Setup**

   ```bash
   # Run verification script
   python verify_setup.py
   ```

4. **Run Phase 1 Tests**

   ```bash
   # Test all Phase 1 functionality
   python test_phase1.py
   ```

5. **Run the Service**

   ```bash
   # Start the service directly
   python -m app.main

   # Or with Poetry
   poetry run python -m app.main
   ```

6. **Test Health Endpoints**

   ```bash
   # Health check
   curl http://localhost:8080/health

   # Readiness check
   curl http://localhost:8080/ready

   # Basic metrics
   curl http://localhost:8080/metrics
   ```

### Docker Development

1. **Build Image**

   ```bash
   docker build -t agent-executor-service .
   ```

2. **Run Container**

   ```bash
   docker run -p 8080:8080 -p 9090:9090 \
     --env-file .env \
     agent-executor-service
   ```

### Testing

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=app

# Run specific test file
poetry run pytest tests/test_basic_functionality.py
```

## Architecture Overview

The service follows the proven mcp-executor-service pattern with these core components:

- **Kafka Service**: Handles message processing and correlation
- **Agent Executor**: Core execution engine (Phase 1: basic implementation)
- **Health Service**: Monitoring and health checks
- **Configuration Management**: Environment-based settings
- **Data Models**: Pydantic models for request/response validation

## Agent Platform Architecture Analysis

### Core Components

The agent platform is built on a modular architecture with the following key components:

#### 1. **Main Application Entry Point** (`app/main.py`)

- Supports two operational modes:
  - **Server Mode**: Runs Kafka consumer to process agent requests
  - **Engine Mode**: Runs the execution service for task processing
- Uses structured logging with configurable levels and JSON formatting
- Handles graceful shutdown and error recovery

#### 2. **Kafka Integration** (`app/kafka_client/`)

- **Consumer** (`consumer.py`): Processes agent creation and chat requests
- **Producer** (`producer.py`): Sends responses back to requesting services
- Supports concurrent message processing with configurable limits
- Handles two main message types:
  - `AgentCreationRequest`: Creates new agent sessions
  - `AgentChatRequest`: Processes chat interactions

#### 3. **AutoGen Service Layer** (`app/autogen_service/`)

- **Agent Factory** (`agent_factory.py`): Creates and configures agents
- **Chat Processor** (`chat_processor.py`): Handles conversation flow
- **Model Factory** (`model_factory.py`): Manages LLM client creation
- **Head Agent** (`head_agent.py`): Coordinates multi-agent interactions
- Supports multiple communication types: single, group, round-robin

#### 4. **Tool Integration** (`app/tools/`)

- **MCP Tool Loader**: Integrates Machine Control Program tools
- **Workflow Tool Loader**: Connects to external workflow systems
- **Dynamic Tool Loader**: Loads API-based tools dynamically
- **Streaming Function Tool**: Supports real-time tool execution

#### 5. **Session Management** (`app/helper/session_manager.py`)

- Redis-based session storage with compression
- Configurable message window management
- Session TTL and cleanup mechanisms
- Memory optimization for large conversation histories

#### 6. **Knowledge Management** (`app/knowledge/`)

- ChromaDB integration for vector storage
- Support for documents, websites, and text sources
- RAG (Retrieval-Augmented Generation) capabilities
- Agent-specific knowledge bases

### Data Flow Architecture

```mermaid
graph TD
    A[Kafka Topic<br/>Agent Requests] --> B[Kafka Consumer]
    B --> C[Agent Factory]
    C --> D[Chat Processor]
    D --> E[Kafka Producer]
    E --> F[Response Topic]
    D --> G[Session Manager<br/>Redis]
    G --> H[AutoGen Agents<br/>+ Tools]
```

### Agent Lifecycle

1. **Agent Creation**:
   - Receive `AgentCreationRequest` via Kafka
   - Fetch agent configuration from external service
   - Initialize session with Redis storage
   - Load tools (MCP, Workflow, Dynamic)
   - Create AutoGen agent instances
   - Return session ID for future interactions

2. **Chat Processing**:
   - Receive `AgentChatRequest` with session ID
   - Retrieve session data and agent configuration
   - Initialize chat context with memory
   - Process message through AutoGen framework
   - Stream responses back via Kafka
   - Update session state

3. **Session Management**:
   - Automatic TTL-based cleanup
   - Compressed message storage
   - Configurable message window limits
   - Memory optimization strategies

### Tool Integration Patterns

#### MCP (Machine Control Program) Tools

- Connect to external MCP servers
- Support for complex tool interactions
- Authentication and credential management
- Real-time tool execution

#### Workflow Tools

- Integration with external workflow engines
- Support for complex business processes
- State management across workflow steps
- Error handling and retry mechanisms

#### Dynamic Tools

- API-based tool loading
- Runtime tool discovery
- JSON schema validation
- HTTP client management

### Configuration Management

The platform uses a hierarchical configuration system:

1. **Environment Variables**: Base configuration
2. **Agent Configurations**: Per-agent settings
3. **Tool Configurations**: Tool-specific parameters
4. **Session Configurations**: Runtime session settings

### Key Technologies

- **AutoGen Framework**: Multi-agent conversation framework
- **Kafka**: Asynchronous message processing
- **Redis**: Session and state management
- **ChromaDB**: Vector database for knowledge storage
- **FastAPI**: API framework (when running in API mode)
- **Pydantic**: Data validation and serialization

## Agent Executor Service Design

### Purpose and Scope

The Agent Executor Service should provide:

1. **Simplified Interface**: Abstract the complexity of the agent platform
2. **Lifecycle Management**: Handle agent creation, execution, and cleanup
3. **Resource Optimization**: Efficient resource allocation and deallocation
4. **Monitoring and Observability**: Track agent performance and health
5. **Error Handling**: Robust error recovery and reporting

### Proposed Architecture

```mermaid
graph TD
    A[Client API] --> B[Executor Service]
    B --> C[Agent Platform]
    B --> D[Task Scheduler]
    D --> E[Monitoring Dashboard]
    B --> F[Kafka Integration]
```

### Core Components Design

#### 1. **Execution Controller**

- Manages agent execution requests
- Handles task queuing and prioritization
- Coordinates with agent platform
- Provides execution status updates

#### 2. **Resource Manager**

- Monitors system resources
- Implements resource allocation policies
- Handles scaling decisions
- Manages cleanup operations

#### 3. **Task Scheduler**

- Queues incoming execution requests
- Implements scheduling algorithms
- Handles task dependencies
- Manages execution timeouts

#### 4. **Integration Layer**

- Kafka client for agent platform communication
- API clients for external services
- Protocol adapters for different interfaces
- Message transformation and routing

#### 5. **Monitoring and Observability**

- Execution metrics collection
- Performance monitoring
- Health checks and alerts
- Audit logging

### Interface Design

#### REST API Endpoints

```python
# Agent Execution
POST /api/v1/agents/execute
GET  /api/v1/agents/{execution_id}/status
POST /api/v1/agents/{execution_id}/cancel
GET  /api/v1/agents/{execution_id}/results

# Resource Management
GET  /api/v1/resources/status
POST /api/v1/resources/scale
GET  /api/v1/resources/metrics

# Health and Monitoring
GET  /api/v1/health
GET  /api/v1/metrics
GET  /api/v1/agents/active
```

#### Message Schemas

```python
class ExecutionRequest(BaseModel):
    agent_id: str
    task_description: str
    parameters: Dict[str, Any]
    priority: int = 1
    timeout: int = 300
    callback_url: Optional[str] = None

class ExecutionStatus(BaseModel):
    execution_id: str
    status: str  # pending, running, completed, failed, cancelled
    progress: float
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]

class ExecutionResult(BaseModel):
    execution_id: str
    result: Dict[str, Any]
    metadata: Dict[str, Any]
    execution_time: float
```

### Integration Patterns

#### 1. **Agent Platform Integration**

- Use Kafka for asynchronous communication
- Implement correlation ID tracking
- Handle response streaming
- Manage session lifecycles

#### 2. **External Service Integration**

- REST API clients for configuration services
- Webhook support for callbacks
- Event streaming for real-time updates
- Authentication and authorization

#### 3. **Monitoring Integration**

- Prometheus metrics export
- Structured logging
- Distributed tracing
- Health check endpoints

### Deployment Considerations

#### 1. **Containerization**

- Docker containers for service isolation
- Kubernetes deployment manifests
- Health check configurations
- Resource limit specifications

#### 2. **Scaling Strategy**

- Horizontal pod autoscaling
- Queue-based load balancing
- Resource-aware scheduling
- Circuit breaker patterns

#### 3. **Configuration Management**

- Environment-based configuration
- Secret management integration
- Configuration hot-reloading
- Feature flag support

### Security Considerations

#### 1. **Authentication and Authorization**

- JWT token validation
- Role-based access control
- API key management
- Service-to-service authentication

#### 2. **Data Protection**

- Encryption in transit and at rest
- Sensitive data masking
- Audit logging
- Compliance requirements

#### 3. **Network Security**

- TLS/SSL enforcement
- Network policies
- Rate limiting
- DDoS protection

## Next Steps

### Phase 1: Core Infrastructure

1. Set up basic service structure
2. Implement Kafka integration
3. Create basic execution controller
4. Add health checks and monitoring

### Phase 2: Advanced Features

1. Implement resource management
2. Add task scheduling capabilities
3. Create monitoring dashboard
4. Implement scaling mechanisms

### Phase 3: Production Readiness

1. Add comprehensive testing
2. Implement security features
3. Create deployment automation
4. Add performance optimization

### Phase 4: Enhanced Capabilities

1. Add advanced scheduling algorithms
2. Implement predictive scaling
3. Create analytics and reporting
4. Add integration with additional platforms

## Conclusion

The Agent Executor Service will serve as a crucial interface layer that simplifies agent management while providing enterprise-grade features like monitoring, scaling, and resource management. By building on top of the robust agent platform foundation, it will enable efficient and reliable agent execution at scale.

The service should focus on operational excellence, providing clear interfaces, comprehensive monitoring, and robust error handling to ensure reliable agent execution in production environments.
