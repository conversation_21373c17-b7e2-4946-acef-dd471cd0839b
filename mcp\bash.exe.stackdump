Stack trace:
Frame         Function      Args
0007FFFF8EA0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF8EA0, 0007FFFF7DA0) msys-2.0.dll+0x1FE8E
0007FFFF8EA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9178) msys-2.0.dll+0x67F9
0007FFFF8EA0  000210046832 (000210286019, 0007FFFF8D58, 0007FFFF8EA0, 000000000000) msys-2.0.dll+0x6832
0007FFFF8EA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8EA0  000210068E24 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9180  00021006A225 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF1E5E0000 ntdll.dll
7FFF1C980000 KERNEL32.DLL
7FFF1BA40000 KERNELBASE.dll
7FFF1E3D0000 USER32.dll
7FFF1C1A0000 win32u.dll
7FFF1CBB0000 GDI32.dll
7FFF1C1D0000 gdi32full.dll
7FFF1BE10000 msvcp_win.dll
7FFF1B770000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF1C640000 advapi32.dll
7FFF1D310000 msvcrt.dll
7FFF1D3C0000 sechost.dll
7FFF1CA50000 RPCRT4.dll
7FFF1AC70000 CRYPTBASE.DLL
7FFF1BF80000 bcryptPrimitives.dll
7FFF1CB70000 IMM32.DLL
