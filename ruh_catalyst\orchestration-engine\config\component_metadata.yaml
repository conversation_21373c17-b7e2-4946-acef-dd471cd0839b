# Component Metadata Configuration
# This file defines metadata for workflow components including their output fields
# and other properties needed for data flow mapping.

components:
  CombineTextComponent:
    output_fields:
      - output_text
      - combined_text
      - text_output
    description: "Combines multiple text inputs into a single output"
    category: "text_processing"
    
  MergeDataComponent:
    output_fields:
      - output_data
      - merged_data
      - data_output
    description: "Merges multiple data structures into a single output"
    category: "data_processing"
    
  TextProcessingComponent:
    output_fields:
      - processed_text
      - output_text
    description: "Processes text input and returns processed text"
    category: "text_processing"
    
  DataProcessingComponent:
    output_fields:
      - processed_data
      - output_data
    description: "Processes data input and returns processed data"
    category: "data_processing"
    
  ApiRequestComponent:
    output_fields:
      - response_data
      - api_response
      - output_data
    description: "Makes API requests and returns response data"
    category: "api"
    
  FileProcessingComponent:
    output_fields:
      - file_content
      - processed_content
      - output_content
    description: "Processes files and returns content"
    category: "file_processing"
    
  SelectDataComponent:
    output_fields:
      - output_data
      - selected_data
    description: "Selects specific data from input structures"
    category: "data_processing"
    
  ParseJSONDataComponent:
    output_fields:
      - parsed_data
      - json_data
      - output_data
    description: "Parses JSON strings into data structures"
    category: "data_processing"

# Default output fields for unknown components
default_output_fields:
  - output_text
  - output_data
  - result
  - response

# Field name collision resolution strategies
collision_resolution:
  strategy: "namespace"  # Options: "namespace", "overwrite", "merge"
  namespace_separator: "_"
  
# Explicit mapping configuration schema
mapping_schema:
  version: "1.0"
  description: "Schema for explicit input-output mappings"
  example:
    input_data_config:
      - from_transition_id: "source_component"
        mapping:
          - from_field: "result.result"
            to_field: "input_data"
          - from_field: "status"
            to_field: "operation_status"
