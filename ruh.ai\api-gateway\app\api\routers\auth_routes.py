from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import RedirectResponse, JSONResponse
from sqlalchemy.orm import Session
from app.core.security import get_current_user, validate_server_auth_key
from app.core.auth_guard import role_required  # Your custom auth guard
from app.schemas.oauth import (
    OAuthMetadata,
    OAuthCallbackResponse,
    OAuthCredentialResponse,
    OAuthErrorResponse,
    OAuthCredentialListResponse,
    OAuthCredentialDeleteResponse,
    ServerOAuthCredentialResponse,
)  # Assuming these are defined
from app.core.config import settings
from app.db.database import get_db  # Assuming this exists

# Import RedisService - adjust the path if necessary
from app.utils.redis.redis_service import RedisService
from app.services.oauth_service import OAuthService  # Your OAuth service

import requests
import logging
import json
from datetime import datetime, timedelta
import secrets  # Import for generating secure random state

# Create router for OAuth authentication
oauth_router = APIRouter(prefix="/oauth", tags=["oauth"])

# Initialize the OAuthService. Assume RedisService is initialized within it
# or available via an instance variable like redis_service
oauth_service = OAuthService()
redis_service = RedisService()

logger = logging.getLogger(__name__)

# Define a constant for how long the OAuth state should be valid in Redis
# This should be long enough for the user to complete the Google authorization steps
# (e.g., 5-10 minutes)
OAUTH_STATE_EXPIRY_SECONDS = 600  # 10 minutes


@oauth_router.post(
    "/google/authorize",
    summary="Initiate Google OAuth flow",
    description="""
    This endpoint initiates the Google OAuth flow for a specific tool (e.g., Google Calendar).

    It requires:
    - mcp_id: The MCP module ID
    - tool_name: The name of the tool (e.g., "google_calendar")

    The endpoint will redirect the user to Google's authorization page.
    After authorization, Google will redirect back to the callback URL.
    """,
    responses={
        302: {
            "description": "Redirect to Google's authorization page",
        },
        400: {
            "description": "Bad Request - Missing required parameters",
        },
        401: {
            "description": "Unauthorized - User not authenticated",
        },
        403: {
            "description": "Forbidden - User role insufficient",
        },
        500: {
            "description": "Internal Server Error",
        },
    },
)
async def google_oauth_authorize(
    mcp_id: str = Query(..., description="The ID of the MCP module."),
    tool_name: str = Query(..., description="The name of the tool (e.g., 'google_calendar')."),
    current_user: dict = Depends(
        role_required(["user"])
    ),  # Dependency handles auth and provides user
):
    """
    Initiates the Google OAuth flow for a specific tool.
    Requires authentication with 'user' role.
    """
    try:
        user_id = current_user.get("user_id")  # Get user_id from the authenticated user object/dict

        if not user_id:
            logger.error(f"Authenticated user object is missing user_id: {current_user}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authenticated user context missing ID",
            )

        logger.info(
            f"Initiating Google OAuth for User: {user_id}, MCP: {mcp_id}, Tool: {tool_name}"
        )

        # 1. Generate a secure, random state value
        state_token = secrets.token_urlsafe(32)

        # 2. Prepare the context data to store
        state_data = {
            "user_id": user_id,
            "mcp_id": mcp_id,
            "tool_name": tool_name,
            "timestamp": datetime.utcnow().isoformat(),
        }
        state_data_json = json.dumps(state_data)

        # 3. Store the context linked to this state_token in Redis with an expiration
        redis_state_key = f"oauth_state:{state_token}"

        try:
            redis_service.set_data_in_redis_with_ttl(
                hash_key=redis_state_key,
                ttl=OAUTH_STATE_EXPIRY_SECONDS,
                data=state_data_json,
            )
            logger.info(f"Stored OAuth state in Redis for key: {redis_state_key}")

        except Exception as redis_error:
            logger.error(
                f"Failed to store OAuth state in Redis for key {redis_state_key}: {redis_error}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to prepare OAuth flow due to internal storage error.",
            ) from redis_error

        # 4. Construct the Google Auth URL with the random state_token
        auth_url = "https://accounts.google.com/o/oauth2/auth"
        params = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "response_type": "code",
            # Ensure you request the correct scopes for the tool! Calendar scope is good for calendar.
            # You might need different scopes for different Google tools.
            # Example scopes:
            # Google Calendar: 'https://www.googleapis.com/auth/calendar'
            # Google Sheets: 'https://www.googleapis.com/auth/spreadsheets'
            # Google Drive: 'https://www.googleapis.com/auth/drive.metadata.readonly'
            # It's common to request user info scopes too: 'openid profile email'
            "scope": "https://www.googleapis.com/auth/calendar openid profile email",
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "access_type": "offline",
            "prompt": "consent",
            "state": state_token,
        }

        # Construct the URL with parameters, ensuring proper encoding
        from urllib.parse import urlencode

        encoded_params = urlencode(params)
        prepared_url = f"{auth_url}?{encoded_params}"

        logger.info(f"Redirecting user to Google Auth URL.")
        print(prepared_url)
        return RedirectResponse(url=prepared_url, status_code=status.HTTP_302_FOUND)

    except HTTPException:
        # Re-raise HTTPException to be handled by FastAPI
        raise
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error in google_oauth_authorize: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate OAuth flow: An internal error occurred.",
        )


@oauth_router.get(
    "/google/callback",
    summary="Handle Google OAuth callback",
    description="""
    This endpoint handles the callback from Google OAuth.

    It receives:
    - code: The authorization code from Google
    - state: The state parameter containing the secure token
    - error (Optional): Error code from Google if authorization failed

    It will:
    1. Validate the state parameter against stored values.
    2. Exchange the code for access and refresh tokens.
    3. Store the tokens securely linked to the correct user/tool.
    4. Return a success/failure response (JSON).
    """,
    responses={
        200: {"description": "OAuth flow completed successfully", "model": OAuthCallbackResponse},
        400: {
            "description": "Bad Request - Invalid parameters or authorization code",
        },
        401: {
            "description": "Unauthorized - Invalid or expired state/session",
        },
        500: {
            "description": "Internal Server Error",
        },
    },
)
async def google_oauth_callback(
    code: Optional[str] = Query(None, description="The authorization code from Google."),
    state: str = Query(..., description="The state parameter from Google."),
    error: Optional[str] = Query(
        None, description="Error code from Google if authorization failed."
    ),
    db: Session = Depends(get_db),
):
    """
    Handles the callback from Google OAuth.
    Validates state, exchanges code for tokens, and stores credentials.
    """
    try:
        if error:
            logger.error(f"Google OAuth error received in callback: {error}")
            user_message = f"Authorization failed: {error}"
            if error == "access_denied":
                user_message = "You denied the authorization request."

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": user_message},
            )

        if not code:
            logger.error("Callback received without code or error parameter.")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"success": False, "message": "Missing authorization code in callback."},
            )

        state_token = state
        redis_state_key = f"oauth_state:{state_token}"

        try:
            stored_state_json = redis_service.get_data_from_redis_using_key(redis_state_key)
            logger.info(f"Attempted to retrieve state from Redis for key: {redis_state_key}")
        except Exception as redis_error:
            logger.error(
                f"Error retrieving OAuth state from Redis for key {redis_state_key}: {redis_error}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to validate OAuth state due to internal storage error.",
            ) from redis_error

        if not stored_state_json:
            logger.error(
                f"Invalid or expired state received in callback. Key not found: {redis_state_key}"
            )
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "success": False,
                    "message": "Invalid or expired OAuth state. Please initiate the flow again.",
                },
            )

        try:
            stored_state = json.loads(stored_state_json)
            user_id = stored_state.get("user_id")
            mcp_id = stored_state.get("mcp_id")
            tool_name = stored_state.get("tool_name")

            if not all([user_id, mcp_id, tool_name]):
                logger.error(
                    f"Missing required data in stored state for key {redis_state_key}: {stored_state}"
                )
                raise ValueError("Missing required data in stored state.")

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(
                f"Failed to parse stored state data for key {redis_state_key}. Raw data: '{stored_state_json}'. Error: {e}",
                exc_info=True,
            )
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "success": False,
                    "message": "Internal error processing OAuth state during parsing.",
                },
            )

        try:
            redis_service.delete_data_with_key(redis_state_key)
            logger.info(f"Deleted OAuth state from Redis for key: {redis_state_key}")
        except Exception as redis_error:
            logger.error(
                f"Failed to delete OAuth state from Redis for key {redis_state_key}: {redis_error}",
                exc_info=True,
            )

        # 3. Exchange the authorization code for tokens
        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            "code": code,
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "grant_type": "authorization_code",
        }

        logger.info("Exchanging authorization code for tokens...")
        token_response = requests.post(token_url, data=token_data)

        if token_response.status_code != 200:
            logger.error(
                f"Token exchange failed: {token_response.status_code} - {token_response.text}"
            )
            detail_message = "Failed to exchange authorization code for tokens."
            try:
                error_json = token_response.json()
                if "error" in error_json and "error_description" in error_json:
                    detail_message = f"Google token exchange failed: {error_json['error']}: {error_json['error_description']}"
                elif "error" in error_json:
                    detail_message = f"Google token exchange failed: {error_json['error']}"
            except json.JSONDecodeError:
                pass

            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "success": False,
                    "message": detail_message,
                },
            )

        token_json = token_response.json()
        logger.info("Successfully exchanged code for tokens.")

        tokens = {
            "access_token": token_json.get("access_token"),
            "refresh_token": token_json.get("refresh_token"),
            "token_type": token_json.get("token_type", "Bearer"),
            "expires_in": token_json.get("expires_in"),
            "scope": token_json.get("scope"),
            "id_token": token_json.get("id_token"),
            "issued_at": token_json.get("issued_at"),
            "updated_at": datetime.utcnow().isoformat(),
        }

        # Optional: Decode the id_token to get user info like email/Google user ID if needed
        # from jose import jwt as jose_jwt # Use a different alias to avoid conflict
        # try:
        #      if tokens.get("id_token"):
        #          # id_token verification requires knowing Google's public keys
        #          # A simpler approach for *getting info* is to decode without verification (LESS SECURE if not verified elsewhere)
        #          # Or, use Google's tokeninfo endpoint: https://oauth2.googleapis.com/tokeninfo?id_token=XYZ
        #          id_token_payload = jose_jwt.decode(tokens["id_token"], options={"verify_signature": False}) # BE CAREFUL with verify_signature=False
        #          google_user_id = id_token_payload.get("sub") # 'sub' is the unique Google user ID
        #          google_email = id_token_payload.get("email")
        #          logger.info(f"Decoded id_token - Google User ID: {google_user_id}, Email: {google_email}")
        #          # You might store google_user_id as an identifier if your app needs it
        # except Exception as id_e:
        #     logger.warning(f"Failed to decode id_token: {id_e}")
        #     pass # Decoding id_token is not critical for storing credentials

        # 4. Store the tokens securely using the retrieved context (user_id, mcp_id, tool_name)
        logger.info(f"Storing tokens for User: {user_id}, MCP: {mcp_id}, Tool: {tool_name}")
        store_response = oauth_service.store_oauth_credentials(
            db=db, user_id=user_id, mcp_id=mcp_id, tool_name=tool_name, tokens=tokens
        )

        if not store_response["success"]:
            logger.error(f"Failed to store OAuth credentials: {store_response['message']}")
            # Decide how to handle this - return error JSON, or redirect to an error page
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"success": False, "message": store_response["message"]},
            )

        logger.info("OAuth flow completed and credentials stored successfully.")
        return JSONResponse(
            content={
                "success": True,
                "message": "OAuth flow completed successfully. Credentials stored.",
                "user_id": user_id,
                "mcp_id": mcp_id,
                "tool_name": tool_name,
                # Optional: Include data for frontend redirect or confirmation
                # "redirect_url": f"/some/frontend/path?status=success&tool={tool_name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in google_oauth_callback: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": f"An internal error occurred during OAuth callback: {str(e)}",
            },
        )


@oauth_router.get(
    "/credentials",
    summary="Get OAuth credentials",
    description="""
    This endpoint retrieves OAuth credentials for a specific tool.

    It requires:
    - mcp_id: The MCP module ID
    - tool_name: The name of the tool (e.g., "google_calendar")

    The endpoint will return the OAuth credentials if they exist.
    """,
    response_model=OAuthCredentialResponse,
    responses={
        200: {
            "description": "OAuth credentials retrieved successfully",
            "model": OAuthCredentialResponse,
        },
        400: {
            "description": "Bad Request - Missing required parameters",
            "model": OAuthErrorResponse,
        },
        401: {
            "description": "Unauthorized - User not authenticated",
        },
        403: {
            "description": "Forbidden - User role insufficient",
        },
        404: {
            "description": "Not Found - Credentials not found",
            "model": OAuthErrorResponse,
        },
        500: {
            "description": "Internal Server Error",
            "model": OAuthErrorResponse,
        },
    },
)
async def get_oauth_credentials(
    mcp_id: str = Query(..., description="The ID of the MCP module."),
    tool_name: str = Query(..., description="The name of the tool (e.g., 'google_calendar')."),
    db: Session = Depends(get_db),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Retrieves OAuth credentials for a specific tool.
    Requires authentication with 'user' role.
    """
    try:
        user_id = current_user.get("user_id")

        if not user_id:
            logger.error(f"Authenticated user object is missing user_id: {current_user}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authenticated user context missing ID",
            )

        logger.info(
            f"Retrieving OAuth credentials for User: {user_id}, MCP: {mcp_id}, Tool: {tool_name}"
        )

        # Get credentials from the service
        credentials_response = oauth_service.get_oauth_credentials(
            db=db, user_id=user_id, mcp_id=mcp_id, tool_name=tool_name
        )

        if not credentials_response["success"]:
            logger.error(f"Failed to retrieve OAuth credentials: {credentials_response['message']}")

            # Check if it's a "not found" error
            if "not found" in credentials_response["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=credentials_response["message"],
                )

            # Otherwise, it's an internal error
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=credentials_response["message"],
            )

        # Return the credentials
        return credentials_response

    except HTTPException:
        # Re-raise HTTPException to be handled by FastAPI
        raise
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error in get_oauth_credentials: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve OAuth credentials: An internal error occurred.",
        )


@oauth_router.get(
    "/credentials/list",
    summary="List OAuth credentials",
    description="""
    This endpoint lists all OAuth credentials for the authenticated user.

    Optional query parameters:
    - mcp_id: Filter credentials by MCP module ID

    The endpoint will return a list of credential metadata (without the actual tokens).
    """,
    response_model=OAuthCredentialListResponse,
    responses={
        200: {
            "description": "OAuth credentials listed successfully",
            "model": OAuthCredentialListResponse,
        },
        401: {
            "description": "Unauthorized - User not authenticated",
        },
        403: {
            "description": "Forbidden - User role insufficient",
        },
        500: {
            "description": "Internal Server Error",
            "model": OAuthErrorResponse,
        },
    },
)
async def list_oauth_credentials(
    mcp_id: Optional[str] = Query(None, description="Optional MCP ID to filter credentials by."),
    db: Session = Depends(get_db),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Lists all OAuth credentials for the authenticated user.
    Optionally filtered by MCP ID.
    Requires authentication with 'user' role.
    """
    try:
        user_id = current_user.get("user_id")

        if not user_id:
            logger.error(f"Authenticated user object is missing user_id: {current_user}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authenticated user context missing ID",
            )

        logger.info(
            f"Listing OAuth credentials for User: {user_id}"
            + (f", MCP: {mcp_id}" if mcp_id else "")
        )

        # Get credentials from the service
        credentials_response = oauth_service.list_oauth_credentials(
            db=db, user_id=user_id, mcp_id=mcp_id
        )

        if not credentials_response["success"]:
            logger.error(f"Failed to list OAuth credentials: {credentials_response['message']}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=credentials_response["message"],
            )

        # Return the credentials list
        return credentials_response

    except HTTPException:
        # Re-raise HTTPException to be handled by FastAPI
        raise
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error in list_oauth_credentials: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list OAuth credentials: An internal error occurred.",
        )


@oauth_router.delete(
    "/credentials/{credential_id}",
    summary="Delete OAuth credential",
    description="""
    This endpoint deletes an OAuth credential by ID.

    Path parameters:
    - credential_id: The ID of the credential to delete

    The endpoint will return a success message if the credential was deleted successfully.
    """,
    response_model=OAuthCredentialDeleteResponse,
    responses={
        200: {
            "description": "OAuth credential deleted successfully",
            "model": OAuthCredentialDeleteResponse,
        },
        401: {
            "description": "Unauthorized - User not authenticated",
        },
        403: {
            "description": "Forbidden - User role insufficient or not owner of credential",
            "model": OAuthErrorResponse,
        },
        404: {
            "description": "Not Found - Credential not found",
            "model": OAuthErrorResponse,
        },
        500: {
            "description": "Internal Server Error",
            "model": OAuthErrorResponse,
        },
    },
)
async def delete_oauth_credential(
    credential_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Deletes an OAuth credential by ID.
    Requires authentication with 'user' role.
    """
    try:
        user_id = current_user.get("user_id")

        if not user_id:
            logger.error(f"Authenticated user object is missing user_id: {current_user}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authenticated user context missing ID",
            )

        logger.info(f"Deleting OAuth credential ID: {credential_id} for User: {user_id}")

        # Delete credential using the service
        delete_response = oauth_service.delete_oauth_credential(
            db=db, credential_id=credential_id, user_id=user_id
        )

        if not delete_response["success"]:
            logger.error(f"Failed to delete OAuth credential: {delete_response['message']}")

            # Check if it's a "not found" error
            if "not found" in delete_response["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=delete_response["message"],
                )

            # Check if it's a permission error
            if "permission" in delete_response["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=delete_response["message"],
                )

            # Otherwise, it's an internal error
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=delete_response["message"],
            )

        # Return success response
        return delete_response

    except HTTPException:
        # Re-raise HTTPException to be handled by FastAPI
        raise
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(f"Unexpected error in delete_oauth_credential: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete OAuth credential: An internal error occurred.",
        )


@oauth_router.get(
    "/server/credentials/by-key",
    summary="Get OAuth credentials by composite key (server-to-server)",
    description="""
    This endpoint retrieves OAuth credentials using a composite key.
    It is intended for server-to-server communication and requires server authentication.

    Authentication:
    - Requires the X-Server-Auth-Key header with a valid server authentication key

    Query parameters:
    - composite_key: The composite key in the format "{user_id}_{mcp_id}_{tool_name}"
    - user_id: The user ID for verification purposes

    The endpoint will return the full OAuth credential including tokens if authentication is successful.
    """,
    response_model=ServerOAuthCredentialResponse,
    responses={
        200: {
            "description": "OAuth credentials retrieved successfully",
            "model": ServerOAuthCredentialResponse,
        },
        400: {
            "description": "Bad Request - Missing required parameters",
            "model": OAuthErrorResponse,
        },
        401: {
            "description": "Unauthorized - Invalid server authentication",
        },
        403: {
            "description": "Forbidden - User verification failed",
            "model": OAuthErrorResponse,
        },
        404: {
            "description": "Not Found - Credentials not found",
            "model": OAuthErrorResponse,
        },
        500: {
            "description": "Internal Server Error",
            "model": OAuthErrorResponse,
        },
    },
)
async def get_oauth_credentials_by_composite_key(
    composite_key: str = Query(
        ..., description="The composite key in the format '{user_id}_{mcp_id}_{tool_name}'"
    ),
    db: Session = Depends(get_db),
    # Server authentication - only require the server auth key
    # This variable is not directly used but the Depends() ensures authentication is performed
    _: bool = Depends(validate_server_auth_key),
):
    """
    Retrieves OAuth credentials using a composite key.
    This endpoint is intended for server-to-server communication and requires server authentication.
    """
    try:
        logger.info(f"Server requesting OAuth credentials with composite key: {composite_key}")

        # Basic validation of the composite key format
        key_parts = composite_key.split("_")
        if len(key_parts) < 3:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid composite key format. Expected format: '{user_id}_{mcp_id}_{tool_name}'",
            )

        # Get credentials from the service
        credentials_response = oauth_service.get_oauth_credentials_by_composite_key(
            db=db, composite_key=composite_key
        )

        if not credentials_response["success"]:
            logger.error(f"Failed to retrieve OAuth credentials: {credentials_response['message']}")

            # Check if it's a "not found" error
            if "not found" in credentials_response["message"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=credentials_response["message"],
                )

            # Otherwise, it's an internal error
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=credentials_response["message"],
            )

        # Return the credentials
        return credentials_response

    except HTTPException:
        # Re-raise HTTPException to be handled by FastAPI
        raise
    except Exception as e:
        # Catch any other unexpected errors
        logger.error(
            f"Unexpected error in get_oauth_credentials_by_composite_key: {str(e)}", exc_info=True
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve OAuth credentials: An internal error occurred.",
        )
