import pytest
from fastapi.testclient import TestClient

class TestNotificationRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, user_headers):
        self.client = test_client
        self.headers = user_headers

    def test_create_notification(self):
        notification_data = {
            "type": "INFO",
            "title": "Test Notification",
            "message": "This is a test notification",
            "metadata": {"key": "value"},
            "priority": "LOW"
        }
        response = self.client.post(
            "/api/v1/notifications",
            json=notification_data,
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["title"] == notification_data["title"]

    def test_get_user_notifications(self):
        response = self.client.get(
            "/api/v1/notifications",
            headers=self.headers
        )
        assert response.status_code == 200
        assert isinstance(response.json(), list)

    def test_mark_notification_read(self):
        response = self.client.put(
            "/api/v1/notifications/1/read",
            headers=self.headers
        )
        assert response.status_code in [200, 404]