# session_manager.py
import asyncio
import json
import logging
import uuid
import zlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple

import redis.asyncio as redis
from app.config.config import settings
from app.models.agent_config import AgentConfig
from app.models.execution import AgentExecutionRequest


class SessionManagerError(Exception):
    """Custom exception for session manager errors."""
    pass


class SessionManager:
    """
    Redis-based session manager for agent execution sessions.
    Handles session creation, memory management, and cleanup.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.redis_url = settings.redis_url
        self.session_ttl = settings.session_ttl_seconds
        self.compression_enabled = settings.session_compression_enabled
        
        # Redis client
        self.redis_client: Optional[redis.Redis] = None
        
        # In-memory session tracking for active sessions
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        
        self.logger.info(f"SessionManager initialized with Redis URL: {self.redis_url}")

    async def start(self):
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis_client.ping()
            self.logger.info("SessionManager connected to Redis successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}", exc_info=True)
            raise SessionManagerError(f"Failed to connect to Redis: {e}")

    async def stop(self):
        """Close Redis connection."""
        if self.redis_client:
            try:
                await self.redis_client.close()
                self.logger.info("SessionManager Redis connection closed")
            except Exception as e:
                self.logger.error(f"Error closing Redis connection: {e}")

    async def create_session(
        self,
        execution_id: str,
        agent_config: AgentConfig,
        user_id: str,
        request: AgentExecutionRequest
    ) -> str:
        """
        Create a new session for agent execution.
        
        Args:
            execution_id: Unique execution identifier
            agent_config: Agent configuration
            user_id: User identifier
            request: Original execution request
            
        Returns:
            str: Session ID
            
        Raises:
            SessionManagerError: If session creation fails
        """
        session_id = str(uuid.uuid4())
        
        try:
            # Extract essential config data to reduce storage
            essential_config = self._extract_essential_config(agent_config)
            
            session_data = {
                "session_id": session_id,
                "execution_id": execution_id,
                "user_id": user_id,
                "agent_id": agent_config.agent_id,
                "agent_config": essential_config,
                "communication_type": agent_config.communication_type.value,
                "created_at": datetime.utcnow().isoformat(),
                "last_activity": datetime.utcnow().isoformat(),
                "status": "active",
                "messages": [],
                "memory_state": {},
                "execution_context": {
                    "task": request.task,
                    "parameters": request.parameters,
                    "priority": request.priority,
                    "correlation_id": request.correlation_id
                }
            }
            
            # Store in Redis with TTL
            key = f"session:{session_id}"
            serialized_data = await self._serialize_session_data(session_data)
            
            await self.redis_client.setex(key, self.session_ttl, serialized_data)
            
            # Track in memory for active sessions
            async with self._lock:
                self.active_sessions[session_id] = {
                    "execution_id": execution_id,
                    "agent_id": agent_config.agent_id,
                    "user_id": user_id,
                    "created_at": datetime.utcnow(),
                    "last_activity": datetime.utcnow()
                }
            
            self.logger.info(
                f"Session created: {session_id} for execution {execution_id} "
                f"(agent: {agent_config.agent_id}, user: {user_id})"
            )
            
            return session_id
            
        except Exception as e:
            self.logger.error(f"Failed to create session: {e}", exc_info=True)
            raise SessionManagerError(f"Failed to create session: {e}")

    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session data by session ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dict containing session data or None if not found
        """
        try:
            key = f"session:{session_id}"
            serialized_data = await self.redis_client.get(key)
            
            if not serialized_data:
                self.logger.warning(f"Session not found: {session_id}")
                return None
            
            session_data = await self._deserialize_session_data(serialized_data)
            
            # Update last activity
            await self._update_last_activity(session_id)
            
            self.logger.debug(f"Retrieved session: {session_id}")
            return session_data
            
        except Exception as e:
            self.logger.error(f"Failed to get session {session_id}: {e}", exc_info=True)
            return None

    async def update_session(
        self,
        session_id: str,
        updates: Dict[str, Any],
        extend_ttl: bool = True
    ) -> bool:
        """
        Update session data.
        
        Args:
            session_id: Session identifier
            updates: Dictionary of updates to apply
            extend_ttl: Whether to extend session TTL
            
        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            session_data = await self.get_session(session_id)
            if not session_data:
                return False
            
            # Apply updates
            session_data.update(updates)
            session_data["last_activity"] = datetime.utcnow().isoformat()
            
            # Store updated data
            key = f"session:{session_id}"
            serialized_data = await self._serialize_session_data(session_data)
            
            if extend_ttl:
                await self.redis_client.setex(key, self.session_ttl, serialized_data)
            else:
                await self.redis_client.set(key, serialized_data)
            
            # Update in-memory tracking
            async with self._lock:
                if session_id in self.active_sessions:
                    self.active_sessions[session_id]["last_activity"] = datetime.utcnow()
            
            self.logger.debug(f"Updated session: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update session {session_id}: {e}", exc_info=True)
            return False

    async def add_message(
        self,
        session_id: str,
        message: Dict[str, Any],
        max_messages: int = 50
    ) -> bool:
        """
        Add a message to session history.
        
        Args:
            session_id: Session identifier
            message: Message to add
            max_messages: Maximum messages to keep
            
        Returns:
            bool: True if successful
        """
        try:
            session_data = await self.get_session(session_id)
            if not session_data:
                return False
            
            # Add timestamp to message
            message["timestamp"] = datetime.utcnow().isoformat()
            
            # Add message and maintain max limit
            messages = session_data.get("messages", [])
            messages.append(message)
            
            # Keep only the most recent messages
            if len(messages) > max_messages:
                messages = messages[-max_messages:]
            
            # Update session
            return await self.update_session(session_id, {"messages": messages})
            
        except Exception as e:
            self.logger.error(f"Failed to add message to session {session_id}: {e}", exc_info=True)
            return False

    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            bool: True if deleted successfully
        """
        try:
            key = f"session:{session_id}"
            result = await self.redis_client.delete(key)
            
            # Remove from in-memory tracking
            async with self._lock:
                self.active_sessions.pop(session_id, None)
            
            self.logger.info(f"Session deleted: {session_id}")
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Failed to delete session {session_id}: {e}", exc_info=True)
            return False

    async def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions from in-memory tracking.
        
        Returns:
            int: Number of sessions cleaned up
        """
        try:
            current_time = datetime.utcnow()
            expired_sessions = []
            
            async with self._lock:
                for session_id, session_info in self.active_sessions.items():
                    # Check if session is older than TTL
                    age = current_time - session_info["last_activity"]
                    if age.total_seconds() > self.session_ttl:
                        expired_sessions.append(session_id)
                
                # Remove expired sessions
                for session_id in expired_sessions:
                    del self.active_sessions[session_id]
            
            if expired_sessions:
                self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
            
            return len(expired_sessions)
            
        except Exception as e:
            self.logger.error(f"Error during session cleanup: {e}", exc_info=True)
            return 0

    async def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics."""
        try:
            # Count active sessions in Redis
            pattern = "session:*"
            redis_sessions = await self.redis_client.keys(pattern)
            
            async with self._lock:
                memory_sessions = len(self.active_sessions)
            
            return {
                "redis_sessions": len(redis_sessions),
                "memory_sessions": memory_sessions,
                "session_ttl": self.session_ttl,
                "compression_enabled": self.compression_enabled
            }
            
        except Exception as e:
            self.logger.error(f"Error getting session stats: {e}", exc_info=True)
            return {"error": str(e)}

    def _extract_essential_config(self, agent_config: AgentConfig) -> Dict[str, Any]:
        """Extract essential configuration data to reduce storage size."""
        return {
            "agent_id": agent_config.agent_id,
            "name": agent_config.name,
            "model_config": agent_config.model_config.dict(),
            "communication_type": agent_config.communication_type.value,
            "termination_config": agent_config.termination_config.dict(),
            "memory_config": agent_config.memory_config.dict(),
            "agents": [agent.dict() for agent in agent_config.agents],
            "tools": [tool.dict() for tool in agent_config.tools]
        }

    async def _serialize_session_data(self, data: Dict[str, Any]) -> str:
        """Serialize session data with optional compression."""
        json_data = json.dumps(data)
        
        if self.compression_enabled:
            compressed = zlib.compress(json_data.encode('utf-8'))
            # Prefix with marker to indicate compression
            return "COMPRESSED:" + compressed.hex()
        else:
            return json_data

    async def _deserialize_session_data(self, data: str) -> Dict[str, Any]:
        """Deserialize session data with optional decompression."""
        if data.startswith("COMPRESSED:"):
            # Remove prefix and decompress
            hex_data = data[11:]  # Remove "COMPRESSED:" prefix
            compressed = bytes.fromhex(hex_data)
            json_data = zlib.decompress(compressed).decode('utf-8')
        else:
            json_data = data
        
        return json.loads(json_data)

    async def _update_last_activity(self, session_id: str) -> None:
        """Update last activity timestamp for a session."""
        async with self._lock:
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["last_activity"] = datetime.utcnow()
