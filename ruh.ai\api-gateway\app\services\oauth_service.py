import uuid
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.db.models import OAuthCredential
from app.utils.secret_manager import SecretManager
import logging

logger = logging.getLogger(__name__)


class OAuthService:
    def __init__(self):
        self.secret_manager = SecretManager()

    def store_oauth_credentials(
        self, db: Session, user_id: str, mcp_id: str, tool_name: str, tokens: dict
    ) -> dict:
        """
        Store OAuth credentials in Google Secret Manager and save the reference in the database.

        Args:
            db: Database session
            user_id: User ID
            mcp_id: MCP module ID
            tool_name: Tool name (e.g., "google_calendar")
            tokens: Dictionary containing OAuth tokens

        Returns:
            Dictionary with operation status
        """
        try:
            # Create composite key
            composite_key = f"{user_id}_{mcp_id}_{tool_name}"

            # Check if credential with this composite key already exists
            existing_credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            # Store tokens in Secret Manager
            secret_id = self.secret_manager.store_oauth_tokens(
                user_id=user_id, mcp_id=mcp_id, tool_name=tool_name, tokens=tokens
            )

            if existing_credential:
                # Update existing credential
                existing_credential.secret_reference = secret_id
                existing_credential.updated_at = datetime.now()
                existing_credential.last_used_at = datetime.now()
                db.commit()

                return {
                    "success": True,
                    "message": "OAuth credentials updated successfully",
                    "credential_id": existing_credential.id,
                }
            else:
                # Create new credential
                new_credential = OAuthCredential(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    mcp_id=mcp_id,
                    tool_name=tool_name,
                    composite_key=composite_key,
                    secret_reference=secret_id,
                )

                db.add(new_credential)
                db.commit()
                db.refresh(new_credential)

                return {
                    "success": True,
                    "message": "OAuth credentials stored successfully",
                    "credential_id": new_credential.id,
                }

        except IntegrityError as e:
            db.rollback()
            logger.error(f"Database integrity error: {str(e)}")
            return {
                "success": False,
                "message": "Credential with this composite key already exists",
            }
        except Exception as e:
            db.rollback()
            logger.error(f"Error storing OAuth credentials: {str(e)}")
            return {"success": False, "message": f"Error storing OAuth credentials: {str(e)}"}

    def get_oauth_credentials(self, db: Session, user_id: str, mcp_id: str, tool_name: str) -> dict:
        """
        Retrieve OAuth credentials from Google Secret Manager using the stored reference.

        Args:
            db: Database session
            user_id: User ID
            mcp_id: MCP module ID
            tool_name: Tool name (e.g., "google_calendar")

        Returns:
            Dictionary with operation status and credentials
        """
        try:
            # Create composite key
            composite_key = f"{user_id}_{mcp_id}_{tool_name}"

            # Find credential by composite key
            credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            if not credential:
                return {
                    "success": False,
                    "message": "OAuth credential not found for the given parameters",
                }

            # Update last used timestamp
            credential.last_used_at = datetime.now()
            db.commit()

            # Retrieve the tokens from Secret Manager
            try:
                tokens = self.secret_manager.get_oauth_tokens(credential.secret_reference)

                return {
                    "success": True,
                    "message": "OAuth credentials retrieved successfully",
                    "access_token": tokens.get("access_token", ""),
                    "refresh_token": tokens.get("refresh_token", ""),
                    "token_type": tokens.get("token_type", ""),
                    "expires_in": tokens.get("expires_in", 0),
                    "scope": tokens.get("scope", ""),
                }

            except Exception as e:
                logger.error(f"Error retrieving from Secret Manager: {str(e)}")
                return {
                    "success": False,
                    "message": f"Error retrieving from Secret Manager: {str(e)}",
                }

        except Exception as e:
            logger.error(f"Error in get_oauth_credentials: {str(e)}")
            return {"success": False, "message": f"Error retrieving OAuth credentials: {str(e)}"}

    def list_oauth_credentials(self, db: Session, user_id: str, mcp_id: str = None) -> dict:
        """
        List all OAuth credentials for a user, optionally filtered by MCP ID.

        Args:
            db: Database session
            user_id: User ID
            mcp_id: Optional MCP module ID to filter by

        Returns:
            Dictionary with operation status and list of credentials
        """
        try:
            # Build query
            query = db.query(OAuthCredential).filter(OAuthCredential.user_id == user_id)

            # Add MCP filter if provided
            if mcp_id:
                query = query.filter(OAuthCredential.mcp_id == mcp_id)

            # Execute query
            credentials = query.all()

            if not credentials:
                return {
                    "success": True,
                    "message": "No OAuth credentials found for the user",
                    "credentials": [],
                }

            # Convert to list of dictionaries
            credential_list = []
            for cred in credentials:
                credential_list.append(
                    {
                        "id": cred.id,
                        "user_id": cred.user_id,
                        "mcp_id": cred.mcp_id,
                        "tool_name": cred.tool_name,
                        "created_at": cred.created_at.isoformat() if cred.created_at else None,
                        "updated_at": cred.updated_at.isoformat() if cred.updated_at else None,
                        "last_used_at": (
                            cred.last_used_at.isoformat() if cred.last_used_at else None
                        ),
                    }
                )

            return {
                "success": True,
                "message": "OAuth credentials retrieved successfully",
                "credentials": credential_list,
            }

        except Exception as e:
            logger.error(f"Error in list_oauth_credentials: {str(e)}")
            return {"success": False, "message": f"Error listing OAuth credentials: {str(e)}"}

    def delete_oauth_credential(self, db: Session, credential_id: str, user_id: str) -> dict:
        """
        Delete an OAuth credential by ID.

        Args:
            db: Database session
            credential_id: ID of the credential to delete
            user_id: User ID (for security verification)

        Returns:
            Dictionary with operation status
        """
        try:
            # Find credential by ID
            credential = (
                db.query(OAuthCredential).filter(OAuthCredential.id == credential_id).first()
            )

            if not credential:
                return {"success": False, "message": "OAuth credential not found"}

            # Security check: ensure the credential belongs to the user
            if credential.user_id != user_id:
                logger.warning(
                    f"User {user_id} attempted to delete credential {credential_id} belonging to user {credential.user_id}"
                )
                return {
                    "success": False,
                    "message": "You do not have permission to delete this credential",
                }

            # Get the secret reference before deleting the credential
            secret_reference = credential.secret_reference

            # Delete the credential from the database
            db.delete(credential)
            db.commit()

            # Delete the secret from Secret Manager
            try:
                self.secret_manager.delete_secret(secret_reference)
                logger.info(f"Deleted secret {secret_reference} from Secret Manager")
            except Exception as e:
                logger.error(
                    f"Error deleting secret {secret_reference} from Secret Manager: {str(e)}"
                )
                # We don't fail the operation if secret deletion fails
                # The database record is already deleted

            return {"success": True, "message": "OAuth credential deleted successfully"}

        except Exception as e:
            db.rollback()
            logger.error(f"Error in delete_oauth_credential: {str(e)}")
            return {"success": False, "message": f"Error deleting OAuth credential: {str(e)}"}

    def get_oauth_credentials_by_composite_key(self, db: Session, composite_key: str) -> dict:
        """
        Retrieve OAuth credentials from Google Secret Manager using a composite key directly.
        This method is intended for server-to-server communication.

        Args:
            db: Database session
            composite_key: Composite key in the format "{user_id}_{mcp_id}_{tool_name}"

        Returns:
            Dictionary with operation status and credentials
        """
        try:
            # Find credential by composite key
            credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            if not credential:
                return {
                    "success": False,
                    "message": f"OAuth credential not found for composite key: {composite_key}",
                }

            # Update last used timestamp
            credential.last_used_at = datetime.now()
            db.commit()

            # Retrieve the tokens from Secret Manager
            try:
                tokens = self.secret_manager.get_oauth_tokens(credential.secret_reference)

                # Include user_id, mcp_id, and tool_name in the response
                return {
                    "success": True,
                    "message": "OAuth credentials retrieved successfully",
                    "user_id": credential.user_id,
                    "mcp_id": credential.mcp_id,
                    "tool_name": credential.tool_name,
                    "access_token": tokens.get("access_token", ""),
                    "refresh_token": tokens.get("refresh_token", ""),
                    "token_type": tokens.get("token_type", ""),
                    "expires_in": tokens.get("expires_in", 0),
                    "scope": tokens.get("scope", ""),
                }

            except Exception as e:
                logger.error(f"Error retrieving from Secret Manager: {str(e)}")
                return {
                    "success": False,
                    "message": f"Error retrieving from Secret Manager: {str(e)}",
                }

        except Exception as e:
            logger.error(f"Error in get_oauth_credentials_by_composite_key: {str(e)}")
            return {"success": False, "message": f"Error retrieving OAuth credentials: {str(e)}"}
