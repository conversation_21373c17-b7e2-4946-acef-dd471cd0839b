# test_enhanced_client.py
"""
Comprehensive tests for the enhanced MCP client implementation.
Tests authentication, error handling, protocol compliance, and backward compatibility.
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from app.core_.client import (
    MCPClient,
    AuthenticationManager,
    ConnectionConfig,
    AuthenticationType,
    AuthenticationConfig,
    MCPClientError,
    AuthenticationError,
    ConnectionError,
    ProtocolError,
    TokenValidationError,
    create_authenticated_client,
    create_legacy_client,
)

# Configure logging for tests
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class TestAuthenticationManager:
    """Test the AuthenticationManager class."""

    def test_init(self):
        """Test AuthenticationManager initialization."""
        auth_manager = AuthenticationManager()
        assert auth_manager.auth_configs == {}
        assert auth_manager.default_config is None

    def test_add_bearer_token(self):
        """Test adding Bearer token authentication."""
        auth_manager = AuthenticationManager()
        token = "test-bearer-token-12345"

        auth_manager.add_bearer_token(token)

        assert auth_manager.default_config is not None
        assert auth_manager.default_config.auth_type == AuthenticationType.BEARER
        assert auth_manager.default_config.token == token

    def test_add_api_key_header(self):
        """Test adding API key in header."""
        auth_manager = AuthenticationManager()
        api_key = "test-api-key-12345"

        auth_manager.add_api_key(api_key, location="header", key_name="X-Test-Key")

        config = auth_manager.auth_configs["api_key"]
        assert config.auth_type == AuthenticationType.API_KEY_HEADER
        assert config.api_key == api_key
        assert config.api_key_name == "X-Test-Key"

    def test_add_api_key_query(self):
        """Test adding API key in query parameters."""
        auth_manager = AuthenticationManager()
        api_key = "test-api-key-12345"

        auth_manager.add_api_key(api_key, location="query", key_name="api_key")

        config = auth_manager.auth_configs["api_key"]
        assert config.auth_type == AuthenticationType.API_KEY_QUERY
        assert config.query_params["api_key"] == api_key

    def test_add_custom_auth(self):
        """Test adding custom authentication."""
        auth_manager = AuthenticationManager()
        headers = {"X-Custom-Auth": "custom-value"}
        query_params = {"custom_param": "custom_value"}

        auth_manager.add_custom_auth(
            headers=headers, query_params=query_params, name="custom"
        )

        config = auth_manager.auth_configs["custom"]
        assert config.auth_type == AuthenticationType.CUSTOM
        assert config.custom_headers == headers
        assert config.query_params == query_params

    @pytest.mark.asyncio
    async def test_get_headers_bearer(self):
        """Test getting headers for Bearer token."""
        auth_manager = AuthenticationManager()
        token = "test-bearer-token-12345"
        auth_manager.add_bearer_token(token)

        headers = await auth_manager.get_headers()

        assert headers["Authorization"] == f"Bearer {token}"

    @pytest.mark.asyncio
    async def test_get_headers_api_key(self):
        """Test getting headers for API key."""
        auth_manager = AuthenticationManager()
        api_key = "test-api-key-12345"
        auth_manager.add_api_key(api_key, location="header", key_name="X-API-Key")

        headers = await auth_manager.get_headers()

        assert headers["X-API-Key"] == api_key

    @pytest.mark.asyncio
    async def test_get_query_params(self):
        """Test getting query parameters."""
        auth_manager = AuthenticationManager()
        api_key = "test-api-key-12345"
        auth_manager.add_api_key(api_key, location="query", key_name="api_key")

        params = await auth_manager.get_query_params()

        assert params["api_key"] == api_key

    def test_validate_token(self):
        """Test token validation."""
        auth_manager = AuthenticationManager()

        # Valid tokens
        assert auth_manager.validate_token("valid-token-12345")
        assert auth_manager.validate_token("a" * 20)

        # Invalid tokens
        assert not auth_manager.validate_token("")
        assert not auth_manager.validate_token("short")
        assert not auth_manager.validate_token(None)
        assert not auth_manager.validate_token(123)


class TestConnectionConfig:
    """Test the ConnectionConfig class."""

    def test_default_values(self):
        """Test default configuration values."""
        config = ConnectionConfig()

        assert config.timeout == 30.0
        assert config.sse_read_timeout == 300.0
        assert config.max_retries == 3
        assert config.retry_delay == 1.0
        assert config.connection_pool_size == 10
        assert config.enable_health_check is True
        assert config.health_check_interval == 60.0

    def test_custom_values(self):
        """Test custom configuration values."""
        config = ConnectionConfig(
            timeout=60.0, max_retries=5, enable_health_check=False
        )

        assert config.timeout == 60.0
        assert config.max_retries == 5
        assert config.enable_health_check is False


class TestMCPClient:
    """Test the enhanced MCPClient class."""

    def test_init_basic(self):
        """Test basic client initialization."""
        server_url = "https://example.com/mcp"
        client = MCPClient(server_url)

        assert client.server_url == server_url
        assert isinstance(client.auth_manager, AuthenticationManager)
        assert isinstance(client.connection_config, ConnectionConfig)
        assert client._is_connected is False

    def test_init_with_legacy_headers(self):
        """Test initialization with legacy headers."""
        server_url = "https://example.com/mcp"
        headers = {"Authorization": "Bearer test-token"}

        client = MCPClient(server_url, headers=headers)

        # Should convert headers to auth manager
        assert "legacy_headers" in client.auth_manager.auth_configs

    def test_init_invalid_url(self):
        """Test initialization with invalid URL."""
        with pytest.raises(ValueError, match="Server URL must start with http"):
            MCPClient("ftp://invalid.com")

    def test_validate_configuration(self):
        """Test configuration validation."""
        server_url = "https://example.com/mcp"

        # Invalid timeout
        config = ConnectionConfig(timeout=-1)
        with pytest.raises(ValueError, match="Timeout must be positive"):
            MCPClient(server_url, connection_config=config)

        # Invalid max_retries
        config = ConnectionConfig(max_retries=-1)
        with pytest.raises(ValueError, match="Max retries must be non-negative"):
            MCPClient(server_url, connection_config=config)

    def test_get_connection_info(self):
        """Test getting connection information."""
        server_url = "https://example.com/mcp"
        client = MCPClient(server_url)

        info = client.get_connection_info()

        assert info["server_url"] == server_url
        assert info["is_connected"] is False
        assert "timeout" in info
        assert "max_retries" in info


class TestFactoryFunctions:
    """Test factory functions for client creation."""

    def test_create_authenticated_client(self):
        """Test creating authenticated client."""
        server_url = "https://example.com/mcp"
        access_token = "test-token-12345"

        client = create_authenticated_client(
            server_url=server_url,
            access_token=access_token,
            timeout=60.0,
            max_retries=5,
        )

        assert client.server_url == server_url
        assert client.connection_config.timeout == 60.0
        assert client.connection_config.max_retries == 5
        assert client.auth_manager.default_config is not None

    def test_create_legacy_client(self):
        """Test creating legacy client."""
        server_url = "https://example.com/mcp"
        headers = {"Authorization": "Bearer test-token"}

        client = create_legacy_client(server_url, headers)

        assert client.server_url == server_url
        assert "legacy_headers" in client.auth_manager.auth_configs

    def test_create_client_with_credential_service(self):
        """Test creating client with credential service integration."""
        from app.core_.client import create_client_with_credential_service

        server_url = "https://example.com/mcp"
        user_id = "test_user"
        mcp_id = "test_mcp"
        tool_name = "google_calendar"

        # Mock credential service
        mock_credential_service = Mock()

        client = create_client_with_credential_service(
            server_url=server_url,
            user_id=user_id,
            mcp_id=mcp_id,
            tool_name=tool_name,
            credential_service=mock_credential_service,
            timeout=60.0,
            max_retries=5,
        )

        assert client.server_url == server_url
        assert client.connection_config.timeout == 60.0
        assert client.connection_config.max_retries == 5
        assert client.credential_service == mock_credential_service


class TestCredentialIntegration:
    """Test credential service integration functionality."""

    @pytest.mark.asyncio
    async def test_integrate_credential_service_success(self):
        """Test successful credential service integration."""
        server_url = "https://example.com/mcp"
        client = MCPClient(server_url)

        # Mock credential service
        mock_credential_service = Mock()
        mock_credential_service.get_oauth_credentials = AsyncMock(
            return_value={
                "success": True,
                "access_token": "test-access-token-12345",
                "token_type": "Bearer",
                "expires_in": 3600,
                "refresh_token": "test-refresh-token",
                "scope": "https://www.googleapis.com/auth/calendar",
            }
        )

        client.credential_service = mock_credential_service

        # Test integration
        result = await client.integrate_credential_service(
            user_id="test_user", mcp_id="test_mcp", tool_name="google_calendar"
        )

        assert result is True
        assert client.auth_manager.default_config is not None
        assert client.auth_manager.default_config.auth_type == AuthenticationType.BEARER
        assert client.auth_manager.default_config.token == "test-access-token-12345"

        # Verify credential service was called with correct parameters
        mock_credential_service.get_oauth_credentials.assert_called_once_with(
            user_id="test_user", mcp_id="test_mcp", tool_name="google_calendar"
        )

    @pytest.mark.asyncio
    async def test_integrate_credential_service_no_credentials(self):
        """Test credential service integration when no credentials found."""
        server_url = "https://example.com/mcp"
        client = MCPClient(server_url)

        # Mock credential service returning no credentials
        mock_credential_service = Mock()
        mock_credential_service.get_oauth_credentials = AsyncMock(
            return_value={
                "success": False,
                "message": "No OAuth credentials found for composite key: test_user_test_mcp_google_calendar",
            }
        )

        client.credential_service = mock_credential_service

        # Test integration
        result = await client.integrate_credential_service(
            user_id="test_user", mcp_id="test_mcp", tool_name="google_calendar"
        )

        assert result is False
        assert client.auth_manager.default_config is None

    @pytest.mark.asyncio
    async def test_integrate_credential_service_no_service(self):
        """Test credential service integration when no service configured."""
        server_url = "https://example.com/mcp"
        client = MCPClient(server_url)

        # No credential service configured
        assert client.credential_service is None

        # Test integration
        result = await client.integrate_credential_service(
            user_id="test_user", mcp_id="test_mcp", tool_name="google_calendar"
        )

        assert result is False

    @pytest.mark.asyncio
    async def test_oauth_bearer_headers(self):
        """Test that OAuth Bearer tokens are correctly included in headers."""
        auth_manager = AuthenticationManager()
        access_token = "test-oauth-token-12345"

        auth_manager.add_bearer_token(access_token)

        headers = await auth_manager.get_headers()

        assert "Authorization" in headers
        assert headers["Authorization"] == f"Bearer {access_token}"

        # Verify no credentials are injected into query or body params
        query_params = await auth_manager.get_query_params()
        body_params = await auth_manager.get_body_params()

        assert query_params == {}
        assert body_params == {}


class TestErrorHandling:
    """Test error handling and custom exceptions."""

    def test_exception_hierarchy(self):
        """Test exception class hierarchy."""
        assert issubclass(AuthenticationError, MCPClientError)
        assert issubclass(ConnectionError, MCPClientError)
        assert issubclass(ProtocolError, MCPClientError)
        assert issubclass(TokenValidationError, AuthenticationError)

    def test_authentication_error(self):
        """Test AuthenticationError."""
        error = AuthenticationError("Test auth error")
        assert str(error) == "Test auth error"
        assert isinstance(error, MCPClientError)

    def test_connection_error(self):
        """Test ConnectionError."""
        error = ConnectionError("Test connection error")
        assert str(error) == "Test connection error"
        assert isinstance(error, MCPClientError)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
