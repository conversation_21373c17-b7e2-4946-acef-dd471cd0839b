import logging
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    # Kafka Configuration
    kafka_bootstrap_servers: str = Field(
        default="localhost:9092", alias="KAFKA_BOOTSTRAP_SERVERS"
    )
    kafka_execution_request_topic: str = Field(
        default="agent-execution-requests", alias="KAFKA_EXECUTION_REQUEST_TOPIC"
    )
    kafka_execution_response_topic: str = Field(
        default="agent-execution-responses", alias="KAFKA_EXECUTION_RESPONSE_TOPIC"
    )
    kafka_config_request_topic: str = Field(
        default="agent-config-requests", alias="KAFKA_CONFIG_REQUEST_TOPIC"
    )
    kafka_config_response_topic: str = Field(
        default="agent-config-responses", alias="KAFKA_CONFIG_RESPONSE_TOPIC"
    )
    kafka_consumer_group_id: str = Field(
        default="agent_executor_service", alias="KAFKA_CONSUMER_GROUP_ID"
    )
    
    # Execution Configuration
    max_concurrent_executions: int = Field(default=50, alias="MAX_CONCURRENT_EXECUTIONS")
    default_execution_timeout: int = Field(default=300, alias="DEFAULT_EXECUTION_TIMEOUT")
    default_execution_retries: int = Field(default=3, alias="DEFAULT_EXECUTION_RETRIES")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", alias="REDIS_URL")
    session_ttl_seconds: int = Field(default=3600, alias="SESSION_TTL_SECONDS")  # 1 hour
    session_compression_enabled: bool = Field(default=True, alias="SESSION_COMPRESSION_ENABLED")
    
    # Agent Platform Integration
    agent_platform_base_url: str = Field(
        default="http://localhost:8001", alias="AGENT_PLATFORM_BASE_URL"
    )
    config_cache_ttl: int = Field(
        default=300, alias="CONFIG_CACHE_TTL"  # 5 minutes
    )
    
    # Authentication and Security
    server_auth_key: str = Field(default="", alias="SERVER_AUTH_KEY")
    auth_api_base_url: str = Field(
        default="http://localhost:8000", alias="AUTH_API_BASE_URL"
    )
    
    # Monitoring and Logging
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    prometheus_port: int = Field(default=9090, alias="PROMETHEUS_PORT")
    health_check_port: int = Field(default=8080, alias="HEALTH_CHECK_PORT")
    
    # Resource Management
    memory_limit_mb: int = Field(default=2048, alias="MEMORY_LIMIT_MB")
    cpu_limit_percent: int = Field(default=80, alias="CPU_LIMIT_PERCENT")
    cleanup_interval_seconds: int = Field(default=300, alias="CLEANUP_INTERVAL_SECONDS")  # 5 minutes
    
    # AutoGen Configuration
    autogen_model_timeout: int = Field(default=60, alias="AUTOGEN_MODEL_TIMEOUT")
    autogen_max_messages: int = Field(default=50, alias="AUTOGEN_MAX_MESSAGES")
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        populate_by_name=True,
        extra="ignore",
    )


try:
    settings = Settings()
except Exception as e:
    print(f"FATAL: Error loading Agent Executor Service configuration settings: {e}")
    raise

log_level_int = getattr(logging, settings.log_level.upper(), logging.INFO)

logging.basicConfig(
    level=log_level_int,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
config_logger = logging.getLogger(__name__)
config_logger.info("Agent Executor Service configuration loaded successfully.")
config_logger.debug(f"Agent Executor Service loaded settings: {settings.model_dump()}")
