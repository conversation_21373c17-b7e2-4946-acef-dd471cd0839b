"""
FastMCP Output Schema Demo

Demonstrates automatic generation of output schemas for tools.
"""

from typing import Annotated, List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field

from mcp.server.fastmcp import FastMCP

# Create server
mcp = FastMCP("Output Schema Demo")


# Simple return types
@mcp.tool()
def string_tool(text: str) -> str:
    """Returns the input text"""
    return text


@mcp.tool()
def int_tool(number: int) -> int:
    """Returns the input number"""
    return number


@mcp.tool()
def float_tool(number: float) -> float:
    """Returns the input number"""
    return number


@mcp.tool()
def bool_tool(value: bool) -> bool:
    """Returns the input boolean value"""
    return value


# Complex return types
class Person(BaseModel):
    name: str
    age: int
    email: Optional[str] = None


@mcp.tool()
def create_person(name: str, age: int, email: Optional[str] = None) -> Person:
    """Creates a person object"""
    return Person(name=name, age=age, email=email)


@mcp.tool()
def list_of_strings(items: List[str]) -> List[str]:
    """Returns the input list of strings"""
    return items


@mcp.tool()
def dict_of_values(data: Dict[str, Any]) -> Dict[str, Any]:
    """Returns the input dictionary"""
    return data


class ApiResponse(BaseModel):
    status: str
    code: int
    data: Union[List[Person], Person, None] = None
    message: Optional[str] = None


@mcp.tool()
def complex_response(
    success: bool, person_data: Optional[Person] = None, message: Optional[str] = None
) -> ApiResponse:
    """Returns a complex API response"""
    if success:
        return ApiResponse(
            status="success",
            code=200,
            data=person_data,
            message=message or "Operation completed successfully",
        )
    else:
        return ApiResponse(
            status="error", code=400, data=None, message=message or "Operation failed"
        )


# Enhanced examples with semantic format information
class MediaFile(BaseModel):
    filename: str
    file_path: str
    audio_url: str
    video_url: str
    image_url: str
    thumbnail_url: str
    created_date: str
    modified_timestamp: str
    file_size: int
    duration: float


@mcp.tool()
def get_media_info(media_id: str) -> MediaFile:
    """Get media file information with various format types"""
    return MediaFile(
        filename="sample_video.mp4",
        file_path="/media/videos/sample_video.mp4",
        audio_url="https://example.com/audio/sample.mp3",
        video_url="https://example.com/video/sample.mp4",
        image_url="https://example.com/images/thumbnail.jpg",
        thumbnail_url="https://example.com/thumbnails/thumb.png",
        created_date="2024-01-15",
        modified_timestamp="2024-01-15T10:30:00Z",
        file_size=1024000,
        duration=120.5,
    )


class UserProfile(BaseModel):
    user_id: str
    email: str
    profile_url: str
    avatar_image: str
    status: str
    registration_date: str
    last_login_time: str
    account_balance: float
    completion_percentage: float
    primary_color: str


@mcp.tool()
def get_user_profile(user_id: str) -> UserProfile:
    """Get user profile with various semantic field types"""
    return UserProfile(
        user_id="usr_12345",
        email="<EMAIL>",
        profile_url="https://example.com/users/12345",
        avatar_image="https://example.com/avatars/user12345.jpg",
        status="active",
        registration_date="2023-06-15",
        last_login_time="2024-01-15T14:22:00Z",
        account_balance=150.75,
        completion_percentage=85.5,
        primary_color="#3498db",
    )


class WebsiteData(BaseModel):
    page_url: str
    redirect_link: str
    contact_email: str
    logo_image: str
    background_video: str
    theme_color: str
    last_updated: str
    page_load_time: float
    bounce_rate: float
    visitor_count: int


@mcp.tool()
def analyze_website(url: str) -> WebsiteData:
    """Analyze website and return data with format hints"""
    return WebsiteData(
        page_url="https://example.com/page",
        redirect_link="https://example.com/redirect",
        contact_email="<EMAIL>",
        logo_image="https://example.com/logo.svg",
        background_video="https://example.com/bg-video.webm",
        theme_color="#2c3e50",
        last_updated="2024-01-15T09:15:00Z",
        page_load_time=2.3,
        bounce_rate=35.2,
        visitor_count=15420,
    )


if __name__ == "__main__":
    import asyncio
    import json
    from pprint import pprint

    def print_enhanced_properties(properties):
        """Print enhanced property information in a readable format"""
        print("Enhanced Field Information:")
        for field_name, field_schema in properties.items():
            field_type = field_schema.get("type", "unknown")
            print(f"  • {field_name} ({field_type})")

            # Print semantic information if present
            if field_schema.get("semantic_type"):
                print(f"    - semantic_type: {field_schema['semantic_type']}")

            # Print additional format metadata
            for key, value in field_schema.items():
                if key not in [
                    "type",
                    "title",
                    "semantic_type",
                    "anyOf",
                    "default",
                ]:
                    print(f"    - {key}: {value}")
            print()

    async def main():
        # List tools and print their schemas
        tools = await mcp.list_tools()

        print("Available tools with enhanced output schemas:")
        print("=" * 60)

        for tool in tools:
            print(f"\nTool: {tool.name}")
            print(f"Description: {tool.description}")
            print("Input Schema:")
            pprint(tool.inputSchema, width=80, depth=3)
            print("\nOutput Schema:")
            pprint(tool.outputSchema, width=80, depth=3)

            # Display enhanced field information if available
            if (
                tool.outputSchema
                and tool.outputSchema.get("type") == "object"
                and "properties" in tool.outputSchema
            ):
                # Check if any properties have enhanced information
                has_enhanced_info = any(
                    prop.get("semantic_type")
                    for prop in tool.outputSchema["properties"].values()
                )
                if has_enhanced_info:
                    print()
                    print_enhanced_properties(tool.outputSchema["properties"])
            elif tool.outputSchema and tool.outputSchema.get("type") in [
                "string",
                "integer",
                "number",
                "boolean",
            ]:
                print(f"\nSimple Type: {tool.outputSchema['type']}")
                if tool.outputSchema.get("semantic_type"):
                    print(f"Semantic Type: {tool.outputSchema['semantic_type']}")
                print()

            print("-" * 60)

    asyncio.run(main())
