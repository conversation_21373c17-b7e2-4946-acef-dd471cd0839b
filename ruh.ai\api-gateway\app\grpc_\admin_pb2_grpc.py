# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import admin_pb2 as admin__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in admin_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AdminServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.login = channel.unary_unary(
                '/admin.AdminService/login',
                request_serializer=admin__pb2.LoginRequest.SerializeToString,
                response_deserializer=admin__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.accessToken = channel.unary_unary(
                '/admin.AdminService/accessToken',
                request_serializer=admin__pb2.AccessTokenRequest.SerializeToString,
                response_deserializer=admin__pb2.AccessTokenResponse.FromString,
                _registered_method=True)
        self.createAdmin = channel.unary_unary(
                '/admin.AdminService/createAdmin',
                request_serializer=admin__pb2.CreateAdminRequest.SerializeToString,
                response_deserializer=admin__pb2.AdminResponse.FromString,
                _registered_method=True)
        self.getAdmin = channel.unary_unary(
                '/admin.AdminService/getAdmin',
                request_serializer=admin__pb2.GetAdminRequest.SerializeToString,
                response_deserializer=admin__pb2.AdminResponse.FromString,
                _registered_method=True)
        self.updateAdmin = channel.unary_unary(
                '/admin.AdminService/updateAdmin',
                request_serializer=admin__pb2.UpdateAdminRequest.SerializeToString,
                response_deserializer=admin__pb2.AdminResponse.FromString,
                _registered_method=True)
        self.deleteAdmin = channel.unary_unary(
                '/admin.AdminService/deleteAdmin',
                request_serializer=admin__pb2.DeleteAdminRequest.SerializeToString,
                response_deserializer=admin__pb2.DeleteAdminResponse.FromString,
                _registered_method=True)
        self.listAdmins = channel.unary_unary(
                '/admin.AdminService/listAdmins',
                request_serializer=admin__pb2.ListAdminsRequest.SerializeToString,
                response_deserializer=admin__pb2.ListAdminsResponse.FromString,
                _registered_method=True)
        self.createRole = channel.unary_unary(
                '/admin.AdminService/createRole',
                request_serializer=admin__pb2.CreateRoleRequest.SerializeToString,
                response_deserializer=admin__pb2.RoleResponse.FromString,
                _registered_method=True)
        self.getRole = channel.unary_unary(
                '/admin.AdminService/getRole',
                request_serializer=admin__pb2.GetRoleRequest.SerializeToString,
                response_deserializer=admin__pb2.RoleResponse.FromString,
                _registered_method=True)
        self.updateRole = channel.unary_unary(
                '/admin.AdminService/updateRole',
                request_serializer=admin__pb2.UpdateRoleRequest.SerializeToString,
                response_deserializer=admin__pb2.RoleResponse.FromString,
                _registered_method=True)
        self.deleteRole = channel.unary_unary(
                '/admin.AdminService/deleteRole',
                request_serializer=admin__pb2.DeleteRoleRequest.SerializeToString,
                response_deserializer=admin__pb2.DeleteRoleResponse.FromString,
                _registered_method=True)
        self.listRoles = channel.unary_unary(
                '/admin.AdminService/listRoles',
                request_serializer=admin__pb2.ListRolesRequest.SerializeToString,
                response_deserializer=admin__pb2.ListRolesResponse.FromString,
                _registered_method=True)
        self.assignRole = channel.unary_unary(
                '/admin.AdminService/assignRole',
                request_serializer=admin__pb2.AssignRoleRequest.SerializeToString,
                response_deserializer=admin__pb2.AdminResponse.FromString,
                _registered_method=True)


class AdminServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def login(self, request, context):
        """Authentication
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def accessToken(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createAdmin(self, request, context):
        """Admin Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getAdmin(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateAdmin(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteAdmin(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listAdmins(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def createRole(self, request, context):
        """Role Management
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listRoles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def assignRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AdminServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'login': grpc.unary_unary_rpc_method_handler(
                    servicer.login,
                    request_deserializer=admin__pb2.LoginRequest.FromString,
                    response_serializer=admin__pb2.LoginResponse.SerializeToString,
            ),
            'accessToken': grpc.unary_unary_rpc_method_handler(
                    servicer.accessToken,
                    request_deserializer=admin__pb2.AccessTokenRequest.FromString,
                    response_serializer=admin__pb2.AccessTokenResponse.SerializeToString,
            ),
            'createAdmin': grpc.unary_unary_rpc_method_handler(
                    servicer.createAdmin,
                    request_deserializer=admin__pb2.CreateAdminRequest.FromString,
                    response_serializer=admin__pb2.AdminResponse.SerializeToString,
            ),
            'getAdmin': grpc.unary_unary_rpc_method_handler(
                    servicer.getAdmin,
                    request_deserializer=admin__pb2.GetAdminRequest.FromString,
                    response_serializer=admin__pb2.AdminResponse.SerializeToString,
            ),
            'updateAdmin': grpc.unary_unary_rpc_method_handler(
                    servicer.updateAdmin,
                    request_deserializer=admin__pb2.UpdateAdminRequest.FromString,
                    response_serializer=admin__pb2.AdminResponse.SerializeToString,
            ),
            'deleteAdmin': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteAdmin,
                    request_deserializer=admin__pb2.DeleteAdminRequest.FromString,
                    response_serializer=admin__pb2.DeleteAdminResponse.SerializeToString,
            ),
            'listAdmins': grpc.unary_unary_rpc_method_handler(
                    servicer.listAdmins,
                    request_deserializer=admin__pb2.ListAdminsRequest.FromString,
                    response_serializer=admin__pb2.ListAdminsResponse.SerializeToString,
            ),
            'createRole': grpc.unary_unary_rpc_method_handler(
                    servicer.createRole,
                    request_deserializer=admin__pb2.CreateRoleRequest.FromString,
                    response_serializer=admin__pb2.RoleResponse.SerializeToString,
            ),
            'getRole': grpc.unary_unary_rpc_method_handler(
                    servicer.getRole,
                    request_deserializer=admin__pb2.GetRoleRequest.FromString,
                    response_serializer=admin__pb2.RoleResponse.SerializeToString,
            ),
            'updateRole': grpc.unary_unary_rpc_method_handler(
                    servicer.updateRole,
                    request_deserializer=admin__pb2.UpdateRoleRequest.FromString,
                    response_serializer=admin__pb2.RoleResponse.SerializeToString,
            ),
            'deleteRole': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteRole,
                    request_deserializer=admin__pb2.DeleteRoleRequest.FromString,
                    response_serializer=admin__pb2.DeleteRoleResponse.SerializeToString,
            ),
            'listRoles': grpc.unary_unary_rpc_method_handler(
                    servicer.listRoles,
                    request_deserializer=admin__pb2.ListRolesRequest.FromString,
                    response_serializer=admin__pb2.ListRolesResponse.SerializeToString,
            ),
            'assignRole': grpc.unary_unary_rpc_method_handler(
                    servicer.assignRole,
                    request_deserializer=admin__pb2.AssignRoleRequest.FromString,
                    response_serializer=admin__pb2.AdminResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'admin.AdminService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('admin.AdminService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AdminService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def login(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/login',
            admin__pb2.LoginRequest.SerializeToString,
            admin__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def accessToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/accessToken',
            admin__pb2.AccessTokenRequest.SerializeToString,
            admin__pb2.AccessTokenResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createAdmin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/createAdmin',
            admin__pb2.CreateAdminRequest.SerializeToString,
            admin__pb2.AdminResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getAdmin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/getAdmin',
            admin__pb2.GetAdminRequest.SerializeToString,
            admin__pb2.AdminResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateAdmin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/updateAdmin',
            admin__pb2.UpdateAdminRequest.SerializeToString,
            admin__pb2.AdminResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteAdmin(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/deleteAdmin',
            admin__pb2.DeleteAdminRequest.SerializeToString,
            admin__pb2.DeleteAdminResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listAdmins(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/listAdmins',
            admin__pb2.ListAdminsRequest.SerializeToString,
            admin__pb2.ListAdminsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def createRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/createRole',
            admin__pb2.CreateRoleRequest.SerializeToString,
            admin__pb2.RoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/getRole',
            admin__pb2.GetRoleRequest.SerializeToString,
            admin__pb2.RoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/updateRole',
            admin__pb2.UpdateRoleRequest.SerializeToString,
            admin__pb2.RoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/deleteRole',
            admin__pb2.DeleteRoleRequest.SerializeToString,
            admin__pb2.DeleteRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listRoles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/listRoles',
            admin__pb2.ListRolesRequest.SerializeToString,
            admin__pb2.ListRolesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def assignRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/admin.AdminService/assignRole',
            admin__pb2.AssignRoleRequest.SerializeToString,
            admin__pb2.AdminResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
