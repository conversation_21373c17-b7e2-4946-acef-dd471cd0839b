import json
from typing import Literal, Optional

from bson import ObjectId
from livekit import api

from app.core.config import settings
from app.services.user_service import UserServiceClient
from app.utils.random_string_generator import generate_random_string

user_service = UserServiceClient()


class LiveKitService:
    @staticmethod
    async def create_room(
        agent_id: str,
        chat_type: Literal["single", "multi"],
        current_user: dict,
        conversation_id: Optional[str] = None,
    ) -> dict:
        """
        Create a new LiveKit room
        """

        user_details = await user_service.get_user(user_id=current_user["user_id"])

        identity = user_details.user.userId
        name = user_details.user.fullName
        room_name = f"room-{generate_random_string(10)}"

        if not conversation_id or conversation_id == "":
            conversation_id = str(ObjectId())
        else:
            try:
                ObjectId(conversation_id)
            except Exception as e:
                raise e

        token = (
            api.AccessToken(
                settings.LIVEKIT_API_KEY,
                settings.LIVEKIT_API_SECRET,
            )
            .with_identity(identity)
            .with_name(name)
            .with_grants(
                api.VideoGrants(
                    room_join=True,
                    room=room_name,
                )
            )
        ).to_jwt()

        lkapi = api.LiveKitAPI(
            url=settings.LIVEKIT_URL,
            api_key=settings.LIVEKIT_API_KEY,
            api_secret=settings.LIVEKIT_API_SECRET,
        )
        room_service = lkapi.room

        metadata_str = json.dumps(
            {
                "user": {
                    "userId": user_details.user.userId,
                    "email": user_details.user.email,
                    "fullName": user_details.user.fullName,
                },
                "agent": {"agentId": agent_id, "chatType": chat_type},
                "conversation": {
                    "conversationId": conversation_id,
                },
            }
        )
        await room_service.create_room(
            api.CreateRoomRequest(name=room_name, metadata=metadata_str)
        )

        return {
            "success": True,
            "token": token,
        }
