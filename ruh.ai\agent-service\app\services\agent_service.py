# app/services/agent_service.py
import grpc
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.agent import AgentConfig
from app.grpc import agent_pb2, agent_pb2_grpc
import json
from app.services.user_validation_service import validate_user_id
from app.services.json_validator import validate_schema
from app.services.file_upload import GCSUploadService


class AgentService(agent_pb2_grpc.AgentServiceServicer):

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createAgent(self, request: agent_pb2.CreateAgentRequest, context: grpc.ServicerContext) -> agent_pb2.AgentResponse:
        """
        Creates a new agent configuration by validating the user, schema, and uploading the configuration to Google Cloud Storage (GCS).

        Args:
            request (agent_pb2.CreateAgentRequest): The request containing agent details such as name, description, config, owner ID, and metadata.
            context (grpc.ServicerContext): The gRPC context for handling status codes and errors.

        Returns:
            agent_pb2.AgentResponse: The response indicating success or failure along with an appropriate message.

        Raises:
            grpc.RpcError: If any validation, schema parsing, or GCS upload fails, an appropriate gRPC error code is set.

        """
        db = self.get_db()
        print(f"[REQUEST] Received Workflow: {request.config}")
        try:
            # Step 1: Validate User ID
            is_valid_user = validate_user_id(request.owner_id, db)
            if not is_valid_user:
                print("[ERROR] User not found in the database")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return agent_pb2.AgentResponse(success=False, message="User not found")
            
            print("[INFO] User validated successfully")
            
            # Step 2: Validate Workflow JSON Schema
            schema_file = "app/services/json_schemas/agent_config.json"
            try:
                validated_data = validate_schema(request.config, schema_file)
                if not validated_data:
                    raise ValueError("Schema validation failed")
            except Exception as schema_error:
                print(f"[ERROR] Schema validation failed: {schema_error}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Agent does not validate under schema definition")
                return agent_pb2.AgentResponse(success=False, message="Agent does not validate under schema definition")
            
            print("[INFO] Agent Config schema validated successfully")

            # Step 3: Upload Workflow JSON to GCS
            try:
                file_upload = GCSUploadService()
                gcs_response = file_upload.upload_json_as_file(validated_data, "agent-service")
                workflow_url = gcs_response.get('publicUrl')
                if not workflow_url:
                    raise ValueError("Failed to upload workflow to GCS")
            except Exception as gcs_error:
                print(f"[ERROR] GCS upload failed: {gcs_error}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(str(gcs_error))
                return agent_pb2.AgentResponse(success=False, message=str(gcs_error))
            
            print(f"[INFO] Workflow uploaded successfully: {workflow_url}")

            # Step 4: Store Agent Configuration in Database
            new_agent = AgentConfig(
                name=request.name,
                description=request.description,
                config_url=workflow_url,
                owner_id=request.owner_id,
                visibility=agent_pb2.Visibility.Name(request.visibility).lower(),
                category=agent_pb2.Category.Name(request.category).lower(),
                tags=json.loads(request.tags) if request.tags else None,
                status=agent_pb2.Status.Name(request.status).lower(),
            )
            db.add(new_agent)
            db.commit()
            db.refresh(new_agent)

            return agent_pb2.AgentResponse(
                success=True,
                message=f"Agent Config {request.name} created successfully",
                agent=self._agent_to_protobuf(new_agent)
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.AgentResponse(success=False, message=str(e))
        finally:
            db.close()


    def getAgent(self, request: agent_pb2.GetAgentRequest, context: grpc.ServicerContext) -> agent_pb2.AgentResponse:
        """
        Retrieves an agent configuration by its unique ID.

        Args:
            request (agent_pb2.GetAgentRequest): The request containing the agent ID.
            context (grpc.ServicerContext): The gRPC context for handling status codes and errors.

        Returns:
            agent_pb2.AgentResponse: The response containing the agent details if found, or an error message if not.

        Raises:
            grpc.RpcError: If the agent is not found, a NOT_FOUND status is set.
            grpc.RpcError: If an internal error occurs, an INTERNAL status is set.
        """
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.id).first()
            if agent is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent Config not found")
                return agent_pb2.AgentResponse(success=False, message="Agent Config not found")

            print(f"[AGENT-OWNER-ID] {agent.owner_id}")

            return agent_pb2.AgentResponse(
                success=True,
                message=f"Agent Config {agent.name} retrieved successfully",
                agent=self._agent_to_protobuf(agent)
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.AgentResponse(success=False, message=str(e))
        finally:
            db.close()


    def updateAgent(self, request: agent_pb2.UpdateAgentRequest, context: grpc.ServicerContext) -> agent_pb2.UpdateAgentResponse:
        """
        Updates an existing agent configuration by its ID.

        Args:
            request (agent_pb2.UpdateAgentRequest): The request containing the agent ID and updated configuration details.
            context (grpc.ServicerContext): The gRPC context for handling status codes and errors.

        Returns:
            agent_pb2.UpdateAgentResponse: The response containing the updated agent details if successful, or an error message if not.

        Raises:
            grpc.RpcError: If the agent is not found, a NOT_FOUND status is set.
            grpc.RpcError: If the provided configuration does not conform to the schema, an INVALID_ARGUMENT status is set.
            grpc.RpcError: If an internal error occurs, an INTERNAL status is set.
        """
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Agent Config with id {request.id} not found")
                return agent_pb2.UpdateAgentResponse(success=False, message=f"Agent Config with id {request.id} not found", agent=None)

            # Validate JSON schema
            schema_file = "app/services/json_schemas/agent_config.json"
            validated_data = validate_schema(request.config, schema_file)
            
            if not validated_data:
                print("[ERROR] Agent Config does not conform to the schema definition")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Agent Config does not conform to the schema definition")
                return agent_pb2.UpdateAgentResponse(success=False, message="Agent Config does not conform to the schema definition", agent=None)
            
            # Upload validated data to GCS
            file_upload = GCSUploadService()
            gcs_response = file_upload.upload_json_as_file(validated_data, "agent-service")
            
            if "publicUrl" not in gcs_response:
                raise ValueError("GCS upload failed: No public URL returned")
            
            workflow_url = gcs_response.get("publicUrl")
            print(f"[GCS RESPONSE] {gcs_response}")
            print(f"[GCS URL] {workflow_url}")

            agent.name = request.name
            agent.description = request.description
            agent.config_url = workflow_url
            agent.owner_id = request.owner_id
            agent.visibility = agent_pb2.Visibility.Name(request.visibility).lower()
            agent.category = agent_pb2.Category.Name(request.category).lower()

            agent.tags = json.loads(request.tags) if request.tags else None
            agent.status = agent_pb2.Status.Name(request.status).lower()

            db.commit()
            db.refresh(agent)

            return agent_pb2.UpdateAgentResponse(
                success=True,
                message=f"Agent Config with id {request.name} updated successfully",
                agent=self._agent_to_protobuf(agent)
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.UpdateAgentResponse(success=False, message=str(e), agent=None)
        finally:
            db.close()


    def deleteAgent(self, request: agent_pb2.DeleteAgentRequest, context: grpc.ServicerContext) -> agent_pb2.DeleteAgentResponse:
        """
        Deletes an agent configuration from the database.

        Args:
            request (agent_pb2.DeleteAgentRequest): The request object containing the agent ID to be deleted.
            context (grpc.ServicerContext): The gRPC context for handling request status and errors.

        Returns:
            agent_pb2.DeleteAgentResponse: A response object indicating the success or failure of the deletion.

        Raises:
            grpc.StatusCode.NOT_FOUND: If the specified agent configuration does not exist.
            grpc.StatusCode.INTERNAL: If an internal error occurs during the deletion process.
        """
        db = self.get_db()
        try:
            agent = db.query(AgentConfig).filter(AgentConfig.id == request.id).first()
            if agent is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent Config not found")
                return agent_pb2.DeleteAgentResponse(success=False, message=f"Agent Config not found")

            db.delete(agent)
            db.commit()

            return agent_pb2.DeleteAgentResponse(success=True, message=f"Agent Config {agent.name} deleted successfully")
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.DeleteAgentResponse(success=False, message=str(e))
        finally:
            db.close()


    def listAgents(self, request: agent_pb2.ListAgentsRequest, context: grpc.ServicerContext) -> agent_pb2.ListAgentsResponse:
        """
        Retrieves a paginated list of agent configurations.

        Args:
            request (agent_pb2.ListAgentsRequest): The request containing pagination parameters.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            agent_pb2.ListAgentsResponse: A response containing the list of agents, total count, current page, and total pages.

        Raises:
            grpc.RpcError: If there is an internal server error.
        """
        db = self.get_db()
        try:
            page = request.page
            page_size = request.page_size

            total = db.query(AgentConfig).count()
            agents = db.query(AgentConfig).offset((page - 1) * page_size).limit(page_size).all()

            total_pages = (total + page_size - 1) // page_size

            agent_list = [self._agent_to_protobuf(agent) for agent in agents]

            return agent_pb2.ListAgentsResponse(
                agents=agent_list,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.ListAgentsResponse()  # Return empty response on error
        finally:
            db.close()


    def listAgentsByUserId(self, request: agent_pb2.ListAgentsByUserIdRequest, context: grpc.ServicerContext) -> agent_pb2.ListAgentsResponse:
        """
        Retrieves a paginated list of agent configurations For authenticated user.

        Args:
            request (agent_pb2.ListAgentsByUserIdRequest): The request containing pagination parameters.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            agent_pb2.ListAgentsResponse: A response containing the list of agents, total count, current page, and total pages.

        Raises:
            grpc.RpcError: If there is an internal server error.
        """
        db = self.get_db()
        try:
            page = request.page
            page_size = request.page_size
            owner_id = request.owner_id 

            total = db.query(AgentConfig).filter(AgentConfig.owner_id == owner_id).count()
            agents = db.query(AgentConfig).filter(AgentConfig.owner_id == owner_id).offset((page - 1) * page_size).limit(page_size).all()

            total_pages = (total + page_size - 1) // page_size
            agent_list = [self._agent_to_protobuf(agent) for agent in agents]

            return agent_pb2.ListAgentsResponse(
                agents=agent_list,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return agent_pb2.ListAgentsResponse()  # Return empty response on error
        finally:
            db.close()


    def _agent_to_protobuf(self, agent: AgentConfig) -> agent_pb2.Agent:

      return agent_pb2.Agent(
          id=agent.id,
          name=agent.name,
          description=agent.description,
          config_url=agent.config_url,
          owner_id=agent.owner_id,
          visibility=agent.visibility,
          category=agent.category, # Now should be set at all times
          tags=json.dumps(agent.tags) if agent.tags else "",
          status=agent.status,
          created_at=agent.created_at.isoformat(),  # Time
          updated_at=agent.updated_at.isoformat(),  # Time
      )