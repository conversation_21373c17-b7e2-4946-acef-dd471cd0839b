# health.py
import asyncio
import logging
from typing import Dict, Any

from app.services.health_service import HealthService
from app.config.config import settings

logger = logging.getLogger(__name__)


class HealthAPI:
    """
    Simple HTTP server for health checks and monitoring endpoints.
    Provides basic health and readiness endpoints for Kubernetes probes.
    """

    def __init__(self):
        self.health_service = HealthService()
        self.port = settings.health_check_port
        self.logger = logging.getLogger(__name__)

    async def start_server(self):
        """Start the health check HTTP server."""
        try:
            # For Phase 1, we'll use a simple HTTP server
            # In later phases, this can be replaced with FastAPI
            from aiohttp import web, web_runner
            
            app = web.Application()
            app.router.add_get('/health', self.health_handler)
            app.router.add_get('/ready', self.readiness_handler)
            app.router.add_get('/metrics', self.metrics_handler)
            
            runner = web_runner.AppRunner(app)
            await runner.setup()
            
            site = web_runner.TCPSite(runner, '0.0.0.0', self.port)
            await site.start()
            
            self.logger.info(f"Health check server started on port {self.port}")
            
            # Keep the server running
            while True:
                await asyncio.sleep(3600)  # Sleep for 1 hour
                
        except Exception as e:
            self.logger.error(f"Failed to start health check server: {e}", exc_info=True)
            raise

    async def health_handler(self, request) -> Dict[str, Any]:
        """Handle health check requests."""
        try:
            from aiohttp import web
            
            health_status = await self.health_service.get_health_status()
            
            # Set HTTP status code based on health
            status_code = 200 if health_status["status"] == "healthy" else 503
            
            return web.json_response(health_status, status=status_code)
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}", exc_info=True)
            from aiohttp import web
            return web.json_response(
                {"status": "unhealthy", "error": str(e)}, 
                status=503
            )

    async def readiness_handler(self, request) -> Dict[str, Any]:
        """Handle readiness check requests."""
        try:
            from aiohttp import web
            
            readiness_status = await self.health_service.get_readiness_status()
            
            # Set HTTP status code based on readiness
            status_code = 200 if readiness_status["ready"] else 503
            
            return web.json_response(readiness_status, status=status_code)
            
        except Exception as e:
            self.logger.error(f"Readiness check failed: {e}", exc_info=True)
            from aiohttp import web
            return web.json_response(
                {"ready": False, "error": str(e)}, 
                status=503
            )

    async def metrics_handler(self, request) -> Dict[str, Any]:
        """Handle metrics requests (placeholder for Prometheus metrics)."""
        try:
            from aiohttp import web
            
            # Basic metrics for Phase 1
            metrics = {
                "service": "agent-executor-service",
                "version": "1.0.0",
                "uptime_seconds": await self._get_uptime(),
                "max_concurrent_executions": settings.max_concurrent_executions,
                "current_executions": 0,  # Placeholder
                "total_executions": 0,    # Placeholder
                "failed_executions": 0,   # Placeholder
            }
            
            return web.json_response(metrics)
            
        except Exception as e:
            self.logger.error(f"Metrics request failed: {e}", exc_info=True)
            from aiohttp import web
            return web.json_response(
                {"error": str(e)}, 
                status=500
            )

    async def _get_uptime(self) -> float:
        """Get service uptime in seconds."""
        # This is a placeholder - in a real implementation, 
        # we'd track the actual start time
        return 0.0
