# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: organisation.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'organisation.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12organisation.proto\x12\x0corganisation\"\x8d\x01\n\x19\x43reateOrganisationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bwebsite_url\x18\x02 \x01(\t\x12\x10\n\x08industry\x18\x03 \x01(\t\x12\x12\n\ncreated_by\x18\x04 \x01(\t\x12\x12\n\nadmin_name\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64min_email\x18\x06 \x01(\t\"$\n\x16GetOrganisationRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x90\x01\n\x11OrganisationModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0bwebsite_url\x18\x03 \x01(\t\x12\x10\n\x08industry\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\t\x12\x12\n\ncreated_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\t\"^\n\x14OrganisationResponse\x12\x35\n\x0corganisation\x18\x01 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x83\x01\n\x18ListOrganisationsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x13\n\x0bsearch_term\x18\x03 \x01(\t\x12\x1a\n\x12\x66ilter_by_industry\x18\x04 \x01(\t\x12\x15\n\rfilter_by_tag\x18\x05 \x01(\t\"\x89\x01\n\x19ListOrganisationsResponse\x12\x36\n\rorganisations\x18\x01 \x03(\x0b\x32\x1f.organisation.OrganisationModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xaf\x01\n\x17\x43reateDepartmentRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x1c\n\x14parent_department_id\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\t\x12\x17\n\nvisibility\x18\x06 \x01(\tH\x00\x88\x01\x01\x42\r\n\x0b_visibility\"\"\n\x14GetDepartmentRequest\x12\n\n\x02id\x18\x01 \x01(\t\"~\n\x16ListDepartmentsRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x13\n\x0bsearch_term\x18\x04 \x01(\t\x12\x15\n\rdepartment_id\x18\x05 \x01(\t\"\xc9\x01\n\x0f\x44\x65partmentModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x1c\n\x14parent_department_id\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\x12\x12\n\ncreated_at\x18\x07 \x01(\t\x12\x12\n\nupdated_at\x18\x08 \x01(\t\x12\x14\n\x0cmember_count\x18\t \x01(\x03\"X\n\x12\x44\x65partmentResponse\x12\x31\n\ndepartment\x18\x01 \x01(\x0b\x32\x1d.organisation.DepartmentModel\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x83\x01\n\x17ListDepartmentsResponse\x12\x32\n\x0b\x64\x65partments\x18\x01 \x03(\x0b\x32\x1d.organisation.DepartmentModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\x86\x01\n\x11InviteUserRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0c\n\x04role\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x13\n\x0bpermissions\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\"\x1e\n\x10GetInviteRequest\x12\n\n\x02id\x18\x01 \x01(\t\"^\n\x12ListInvitesRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x0e\n\x06status\x18\x04 \x01(\t\"\xd8\x01\n\x0bInviteModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x17\n\x0forganisation_id\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x0c\n\x04role\x18\x05 \x01(\t\x12\x13\n\x0bpermissions\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x12\n\ncreated_at\x18\t \x01(\t\x12\x12\n\nupdated_at\x18\n \x01(\t\x12\x12\n\nexpires_at\x18\x0b \x01(\t\"]\n\x0eInviteResponse\x12)\n\x06invite\x18\x01 \x01(\x0b\x32\x19.organisation.InviteModel\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"w\n\x13ListInvitesResponse\x12*\n\x07invites\x18\x01 \x03(\x0b\x32\x19.organisation.InviteModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"p\n\x19\x41\x63\x63\x65ptInviteByLinkRequest\x12\x13\n\x0binvite_link\x18\x01 \x01(\t\x12\x1a\n\x12\x63urrent_user_email\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x11\n\tuser_name\x18\x04 \x01(\t\"l\n\x1cGrantDepartmentAccessRequest\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x02 \x03(\t\x12\x12\n\nfolder_ids\x18\x03 \x03(\t\x12\x0f\n\x07user_id\x18\x04 \x01(\t\"A\n\x1dGrantDepartmentAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"S\n\x14\x44\x65partmentAccessData\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x02 \x03(\t\x12\x12\n\nfolder_ids\x18\x03 \x03(\t\"q\n!BatchGrantDepartmentAccessRequest\x12;\n\x0f\x64\x65partment_data\x18\x01 \x03(\x0b\x32\".organisation.DepartmentAccessData\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"e\n\"BatchGrantDepartmentAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1d\n\x15\x66\x61iled_department_ids\x18\x03 \x03(\t\"-\n\x1aListTopLevelFoldersRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"\"\n\x06\x46older\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"f\n\x1bListTopLevelFoldersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x14.organisation.Folder\"\x90\x01\n\x0bSourceModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12&\n\x04type\x18\x03 \x01(\x0e\x32\x18.organisation.SourceType\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x12\n\ncreated_at\x18\x05 \x01(\t\x12\x12\n\nupdated_at\x18\x06 \x01(\t\"{\n\x10\x41\x64\x64SourceRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12&\n\x04type\x18\x02 \x01(\x0e\x32\x18.organisation.SourceType\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x18\n\x10\x63redentials_file\x18\x04 \x01(\t\"`\n\x11\x41\x64\x64SourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x06source\x18\x03 \x01(\x0b\x32\x19.organisation.SourceModel\"-\n\x12ListSourcesRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"c\n\x13ListSourcesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x07sources\x18\x03 \x03(\x0b\x32\x19.organisation.SourceModel\"9\n\x13\x44\x65leteSourceRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"8\n\x14\x44\x65leteSourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\".\n\x1bGetUserOrganisationsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"o\n\x10UserOrganisation\x12\x35\n\x0corganisation\x18\x01 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12\x12\n\nis_primary\x18\x02 \x01(\x08\x12\x10\n\x08is_admin\x18\x03 \x01(\x08\"c\n\x19UserOrganisationsResponse\x12\x35\n\rorganisations\x18\x01 \x03(\x0b\x32\x1e.organisation.UserOrganisation\x12\x0f\n\x07message\x18\x02 \x01(\t*%\n\nVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*)\n\nSourceType\x12\x10\n\x0cGOOGLE_DRIVE\x10\x00\x12\t\n\x05SLACK\x10\x01*D\n\x0cInviteStatus\x12\x0b\n\x07PENDING\x10\x00\x12\x0c\n\x08\x41\x43\x43\x45PTED\x10\x01\x12\x0c\n\x08\x44\x45\x43LINED\x10\x02\x12\x0b\n\x07\x45XPIRED\x10\x03\x32\x9b\n\n\x13OrganisationService\x12\x63\n\x12\x43reateOrganisation\x12\'.organisation.CreateOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12]\n\x0fGetOrganisation\x12$.organisation.GetOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12]\n\x10\x43reateDepartment\x12%.organisation.CreateDepartmentRequest\x1a .organisation.DepartmentResponse\"\x00\x12`\n\x0fListDepartments\x12$.organisation.ListDepartmentsRequest\x1a%.organisation.ListDepartmentsResponse\"\x00\x12M\n\nInviteUser\x12\x1f.organisation.InviteUserRequest\x1a\x1c.organisation.InviteResponse\"\x00\x12]\n\x12\x41\x63\x63\x65ptInviteByLink\x12\'.organisation.AcceptInviteByLinkRequest\x1a\x1c.organisation.InviteResponse\"\x00\x12l\n\x14GetUserOrganisations\x12).organisation.GetUserOrganisationsRequest\x1a\'.organisation.UserOrganisationsResponse\"\x00\x12r\n\x15GrantDepartmentAccess\x12*.organisation.GrantDepartmentAccessRequest\x1a+.organisation.GrantDepartmentAccessResponse\"\x00\x12\x81\x01\n\x1a\x42\x61tchGrantDepartmentAccess\x12/.organisation.BatchGrantDepartmentAccessRequest\x1a\x30.organisation.BatchGrantDepartmentAccessResponse\"\x00\x12l\n\x13ListTopLevelFolders\x12(.organisation.ListTopLevelFoldersRequest\x1a).organisation.ListTopLevelFoldersResponse\"\x00\x12N\n\tAddSource\x12\x1e.organisation.AddSourceRequest\x1a\x1f.organisation.AddSourceResponse\"\x00\x12T\n\x0bListSources\x12 .organisation.ListSourcesRequest\x1a!.organisation.ListSourcesResponse\"\x00\x12W\n\x0c\x44\x65leteSource\x12!.organisation.DeleteSourceRequest\x1a\".organisation.DeleteSourceResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'organisation_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_VISIBILITY']._serialized_start=3883
  _globals['_VISIBILITY']._serialized_end=3920
  _globals['_SOURCETYPE']._serialized_start=3922
  _globals['_SOURCETYPE']._serialized_end=3963
  _globals['_INVITESTATUS']._serialized_start=3965
  _globals['_INVITESTATUS']._serialized_end=4033
  _globals['_CREATEORGANISATIONREQUEST']._serialized_start=37
  _globals['_CREATEORGANISATIONREQUEST']._serialized_end=178
  _globals['_GETORGANISATIONREQUEST']._serialized_start=180
  _globals['_GETORGANISATIONREQUEST']._serialized_end=216
  _globals['_ORGANISATIONMODEL']._serialized_start=219
  _globals['_ORGANISATIONMODEL']._serialized_end=363
  _globals['_ORGANISATIONRESPONSE']._serialized_start=365
  _globals['_ORGANISATIONRESPONSE']._serialized_end=459
  _globals['_LISTORGANISATIONSREQUEST']._serialized_start=462
  _globals['_LISTORGANISATIONSREQUEST']._serialized_end=593
  _globals['_LISTORGANISATIONSRESPONSE']._serialized_start=596
  _globals['_LISTORGANISATIONSRESPONSE']._serialized_end=733
  _globals['_CREATEDEPARTMENTREQUEST']._serialized_start=736
  _globals['_CREATEDEPARTMENTREQUEST']._serialized_end=911
  _globals['_GETDEPARTMENTREQUEST']._serialized_start=913
  _globals['_GETDEPARTMENTREQUEST']._serialized_end=947
  _globals['_LISTDEPARTMENTSREQUEST']._serialized_start=949
  _globals['_LISTDEPARTMENTSREQUEST']._serialized_end=1075
  _globals['_DEPARTMENTMODEL']._serialized_start=1078
  _globals['_DEPARTMENTMODEL']._serialized_end=1279
  _globals['_DEPARTMENTRESPONSE']._serialized_start=1281
  _globals['_DEPARTMENTRESPONSE']._serialized_end=1369
  _globals['_LISTDEPARTMENTSRESPONSE']._serialized_start=1372
  _globals['_LISTDEPARTMENTSRESPONSE']._serialized_end=1503
  _globals['_INVITEUSERREQUEST']._serialized_start=1506
  _globals['_INVITEUSERREQUEST']._serialized_end=1640
  _globals['_GETINVITEREQUEST']._serialized_start=1642
  _globals['_GETINVITEREQUEST']._serialized_end=1672
  _globals['_LISTINVITESREQUEST']._serialized_start=1674
  _globals['_LISTINVITESREQUEST']._serialized_end=1768
  _globals['_INVITEMODEL']._serialized_start=1771
  _globals['_INVITEMODEL']._serialized_end=1987
  _globals['_INVITERESPONSE']._serialized_start=1989
  _globals['_INVITERESPONSE']._serialized_end=2082
  _globals['_LISTINVITESRESPONSE']._serialized_start=2084
  _globals['_LISTINVITESRESPONSE']._serialized_end=2203
  _globals['_ACCEPTINVITEBYLINKREQUEST']._serialized_start=2205
  _globals['_ACCEPTINVITEBYLINKREQUEST']._serialized_end=2317
  _globals['_GRANTDEPARTMENTACCESSREQUEST']._serialized_start=2319
  _globals['_GRANTDEPARTMENTACCESSREQUEST']._serialized_end=2427
  _globals['_GRANTDEPARTMENTACCESSRESPONSE']._serialized_start=2429
  _globals['_GRANTDEPARTMENTACCESSRESPONSE']._serialized_end=2494
  _globals['_DEPARTMENTACCESSDATA']._serialized_start=2496
  _globals['_DEPARTMENTACCESSDATA']._serialized_end=2579
  _globals['_BATCHGRANTDEPARTMENTACCESSREQUEST']._serialized_start=2581
  _globals['_BATCHGRANTDEPARTMENTACCESSREQUEST']._serialized_end=2694
  _globals['_BATCHGRANTDEPARTMENTACCESSRESPONSE']._serialized_start=2696
  _globals['_BATCHGRANTDEPARTMENTACCESSRESPONSE']._serialized_end=2797
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_start=2799
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_end=2844
  _globals['_FOLDER']._serialized_start=2846
  _globals['_FOLDER']._serialized_end=2880
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_start=2882
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_end=2984
  _globals['_SOURCEMODEL']._serialized_start=2987
  _globals['_SOURCEMODEL']._serialized_end=3131
  _globals['_ADDSOURCEREQUEST']._serialized_start=3133
  _globals['_ADDSOURCEREQUEST']._serialized_end=3256
  _globals['_ADDSOURCERESPONSE']._serialized_start=3258
  _globals['_ADDSOURCERESPONSE']._serialized_end=3354
  _globals['_LISTSOURCESREQUEST']._serialized_start=3356
  _globals['_LISTSOURCESREQUEST']._serialized_end=3401
  _globals['_LISTSOURCESRESPONSE']._serialized_start=3403
  _globals['_LISTSOURCESRESPONSE']._serialized_end=3502
  _globals['_DELETESOURCEREQUEST']._serialized_start=3504
  _globals['_DELETESOURCEREQUEST']._serialized_end=3561
  _globals['_DELETESOURCERESPONSE']._serialized_start=3563
  _globals['_DELETESOURCERESPONSE']._serialized_end=3619
  _globals['_GETUSERORGANISATIONSREQUEST']._serialized_start=3621
  _globals['_GETUSERORGANISATIONSREQUEST']._serialized_end=3667
  _globals['_USERORGANISATION']._serialized_start=3669
  _globals['_USERORGANISATION']._serialized_end=3780
  _globals['_USERORGANISATIONSRESPONSE']._serialized_start=3782
  _globals['_USERORGANISATIONSRESPONSE']._serialized_end=3881
  _globals['_ORGANISATIONSERVICE']._serialized_start=4036
  _globals['_ORGANISATIONSERVICE']._serialized_end=5343
# @@protoc_insertion_point(module_scope)
