# Agent Platform and Executor Service Separation Design

## Overview

This document defines the clear separation of concerns between the `agent-platform` and the new `agent-executor-service`, transforming the executor service into the centralized execution engine for all agents across the platform.

## Separation Principles

### 1. **Single Responsibility Principle**

- **Agent Platform**: Configuration, management, and platform services
- **Agent Executor Service**: Execution, orchestration, and runtime management

### 2. **Interface Segregation**

- Clean API boundaries between services
- Minimal coupling through well-defined contracts
- Event-driven communication via Kafka

### 3. **Dependency Inversion**

- Both services depend on abstractions (Kafka topics, schemas)
- No direct service-to-service dependencies
- Configuration-driven integration

## Detailed Separation Plan

### Agent Platform Responsibilities

#### **Core Platform Services**

```
agent-platform/
├── app/
│   ├── api/                     # API Gateway and routing
│   │   ├── routes/
│   │   │   ├── agents.py        # Agent CRUD operations
│   │   │   ├── configurations.py # Configuration management
│   │   │   ├── tools.py         # Tool definitions
│   │   │   └── knowledge.py     # Knowledge base management
│   ├── services/
│   │   ├── agent_config_service.py    # Agent configuration management
│   │   ├── tool_definition_service.py # Tool schema management
│   │   ├── knowledge_service.py       # ChromaDB integration
│   │   └── auth_service.py            # Authentication/authorization
│   ├── models/
│   │   ├── agent_config.py      # Agent configuration models
│   │   ├── tool_schema.py       # Tool definition schemas
│   │   └── knowledge_base.py    # Knowledge management models
│   └── repositories/
│       ├── agent_repo.py        # Agent configuration storage
│       ├── tool_repo.py         # Tool definition storage
│       └── knowledge_repo.py    # Knowledge base storage
```

#### **Retained Responsibilities**

1. **Agent Configuration Management**
   - Agent definitions and schemas
   - Configuration validation and storage
   - Version management and rollback
   - Template management

2. **Tool Definition Management**
   - Tool schemas and specifications
   - Tool discovery and registration
   - Tool versioning and compatibility
   - Tool marketplace functionality

3. **Knowledge Management**
   - ChromaDB integration and management
   - Document ingestion and processing
   - Vector storage and retrieval
   - Knowledge base versioning

4. **Platform Services**
   - Authentication and authorization
   - API gateway and routing
   - User management and permissions
   - Audit logging and compliance

5. **Configuration Services**
   - Environment configuration
   - Feature flags and toggles
   - Service discovery
   - Health monitoring

### Agent Executor Service Responsibilities

#### **Execution Engine Architecture**

```
agent-executor-service/
├── app/
│   ├── core/
│   │   ├── execution_engine.py      # Core execution orchestration
│   │   ├── agent_runtime.py         # AutoGen runtime management
│   │   ├── session_manager.py       # Session lifecycle management
│   │   └── resource_manager.py      # Resource allocation and monitoring
│   ├── services/
│   │   ├── execution_service.py     # Execution request handling
│   │   ├── queue_service.py         # Task queue management
│   │   ├── tool_executor.py         # Tool execution service
│   │   ├── monitoring_service.py    # Performance monitoring
│   │   └── cleanup_service.py       # Resource cleanup
│   ├── integrations/
│   │   ├── kafka_client.py          # Kafka integration
│   │   ├── agent_platform_client.py # Agent platform communication
│   │   ├── mcp_client.py            # MCP server integration
│   │   └── workflow_client.py       # Workflow engine integration
│   ├── executors/
│   │   ├── autogen_executor.py      # AutoGen framework executor
│   │   ├── tool_executor.py         # Tool execution handler
│   │   └── stream_processor.py      # Response streaming
│   └── models/
│       ├── execution_request.py     # Execution request models
│       ├── execution_status.py      # Status and result models
│       └── resource_metrics.py      # Resource monitoring models
```

#### **New Responsibilities**

1. **Execution Orchestration**
   - Agent instantiation and initialization
   - AutoGen team creation and management
   - Execution lifecycle coordination
   - Error handling and recovery

2. **Session Management**
   - Session creation and cleanup
   - Memory management and compression
   - State persistence and recovery
   - Session timeout handling

3. **Tool Integration and Execution**
   - Dynamic tool loading
   - MCP server communication
   - Workflow engine integration
   - Tool result processing

4. **Resource Management**
   - CPU and memory monitoring
   - Execution queue management
   - Concurrency control
   - Resource allocation policies

5. **Performance Monitoring**
   - Execution metrics collection
   - Performance analytics
   - Health monitoring
   - Alert generation

## Communication Architecture

### Kafka Topic Design

#### **Execution Request Topics**

```yaml
# Agent execution requests from external services
agent-execution-requests:
  partitions: 12
  replication: 3
  retention: 24h
  
# Agent execution responses
agent-execution-responses:
  partitions: 12
  replication: 3
  retention: 24h

# Agent execution status updates
agent-execution-status:
  partitions: 6
  replication: 3
  retention: 7d
```

#### **Configuration Topics**

```yaml
# Agent configuration updates from platform
agent-config-updates:
  partitions: 3
  replication: 3
  retention: 30d

# Tool definition updates
tool-definition-updates:
  partitions: 3
  replication: 3
  retention: 30d
```

#### **Monitoring Topics**

```yaml
# Execution metrics and monitoring
execution-metrics:
  partitions: 6
  replication: 3
  retention: 30d

# System health and alerts
system-health:
  partitions: 3
  replication: 3
  retention: 7d
```

### Message Schemas

#### **Execution Request Schema**

```python
class AgentExecutionRequest(BaseModel):
    execution_id: str = Field(..., description="Unique execution identifier")
    agent_id: str = Field(..., description="Agent configuration ID")
    user_id: str = Field(..., description="User identifier")
    task: str = Field(..., description="Task to execute")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(default=5, ge=1, le=10)
    timeout: int = Field(default=300, ge=30, le=3600)
    callback_url: Optional[str] = None
    correlation_id: str = Field(..., description="Request correlation ID")
    source_service: str = Field(..., description="Requesting service")
    created_at: datetime = Field(default_factory=datetime.utcnow)
```

#### **Execution Response Schema**

```python
class AgentExecutionResponse(BaseModel):
    execution_id: str
    correlation_id: str
    status: ExecutionStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    resource_usage: Optional[ResourceUsage] = None
    session_id: Optional[str] = None
    completed_at: datetime = Field(default_factory=datetime.utcnow)
```

#### **Configuration Request Schema**

```python
class AgentConfigRequest(BaseModel):
    agent_id: str
    execution_id: str
    correlation_id: str
    requested_at: datetime = Field(default_factory=datetime.utcnow)

class AgentConfigResponse(BaseModel):
    agent_id: str
    execution_id: str
    correlation_id: str
    config: AgentConfig
    tools: List[ToolDefinition]
    knowledge_bases: List[str]
    responded_at: datetime = Field(default_factory=datetime.utcnow)
```

## Integration Patterns

### 1. **Configuration Retrieval Pattern**

```mermaid
sequenceDiagram
    participant ES as Executor Service
    participant KC as Kafka
    participant AP as Agent Platform
    
    ES->>KC: Publish AgentConfigRequest
    KC->>AP: Deliver config request
    AP->>AP: Fetch agent configuration
    AP->>AP: Fetch tool definitions
    AP->>KC: Publish AgentConfigResponse
    KC->>ES: Deliver config response
    ES->>ES: Cache configuration
```

### 2. **Execution Flow Pattern**

```mermaid
sequenceDiagram
    participant Client as External Client
    participant AG as API Gateway
    participant KC as Kafka
    participant ES as Executor Service
    participant AP as Agent Platform
    
    Client->>AG: POST /execute
    AG->>KC: Publish ExecutionRequest
    KC->>ES: Deliver execution request
    ES->>KC: Request agent config
    KC->>AP: Config request
    AP->>KC: Config response
    KC->>ES: Config response
    ES->>ES: Execute agent
    ES->>KC: Publish ExecutionResponse
    KC->>AG: Execution response
    AG->>Client: HTTP response
```

### 3. **Monitoring and Health Pattern**

```mermaid
sequenceDiagram
    participant ES as Executor Service
    participant KC as Kafka
    participant MS as Monitoring Service
    
    loop Every 30 seconds
        ES->>KC: Publish health metrics
        ES->>KC: Publish execution metrics
    end
    
    KC->>MS: Deliver metrics
    MS->>MS: Process and store metrics
    MS->>MS: Generate alerts if needed
```

## Migration Strategy

### Phase 1: Infrastructure Setup (Week 1-2)

1. **Create Agent Executor Service**
   - Set up project structure
   - Implement basic Kafka integration
   - Create core execution models
   - Set up monitoring infrastructure

2. **Update Agent Platform**
   - Remove execution logic from Kafka consumer
   - Create configuration service APIs
   - Implement Kafka publishers for config updates
   - Update API routes to use executor service

### Phase 2: Core Execution Migration (Week 3-4)

1. **Migrate Execution Logic**
   - Move AutoGen integration to executor service
   - Implement session management in executor
   - Migrate tool loading and execution
   - Create execution queue and prioritization

2. **Update Communication**
   - Implement request/response patterns
   - Add correlation ID tracking
   - Create configuration caching
   - Implement error handling and retries

### Phase 3: Advanced Features (Week 5-6)

1. **Resource Management**
   - Implement resource monitoring
   - Add execution limits and throttling
   - Create auto-scaling mechanisms
   - Implement cleanup services

2. **Monitoring and Observability**
   - Add comprehensive metrics
   - Implement performance monitoring
   - Create health checks and alerts
   - Add distributed tracing

### Phase 4: Production Readiness (Week 7-8)

1. **Testing and Validation**
   - End-to-end testing
   - Performance testing
   - Load testing
   - Security testing

2. **Deployment and Operations**
   - Production deployment
   - Monitoring setup
   - Documentation completion
   - Team training

## Benefits of Separation

### 1. **Scalability**

- Independent scaling of execution and platform services
- Dedicated resource allocation for execution workloads
- Horizontal scaling of executor instances

### 2. **Maintainability**

- Clear separation of concerns
- Reduced coupling between services
- Easier testing and debugging

### 3. **Reliability**

- Fault isolation between services
- Independent deployment and updates
- Better error handling and recovery

### 4. **Performance**

- Optimized execution engine
- Better resource utilization
- Improved monitoring and observability

This separation design creates a robust, scalable architecture where the agent executor service becomes the centralized execution engine while the agent platform focuses on configuration and management tasks.
