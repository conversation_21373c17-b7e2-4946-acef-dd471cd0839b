# agent_config.py
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field


class CommunicationType(str, Enum):
    """Agent communication types"""

    SINGLE = "single"
    ROUND_ROBIN = "round_robin"
    SELECTOR = "selector"
    MAGENTIC_ONE = "magentic_one"


class ToolType(str, Enum):
    """Tool types supported by agents"""

    MCP = "mcp"
    WORKFLOW = "workflow"
    DYNAMIC = "dynamic"
    FUNCTION = "function"


class TerminationType(str, Enum):
    """Termination condition types"""

    MAX_MESSAGES = "max_messages"
    TEXT_MENTION = "text_mention"
    FUNCTION_CALL = "function_call"
    HANDOFF = "handoff"
    EXTERNAL = "external"


class AgentDefinition(BaseModel):
    """Individual agent definition within a team"""

    name: str = Field(..., description="Agent name")
    description: str = Field(..., description="Agent description")
    system_message: str = Field(..., description="Agent system message")
    model: Optional[str] = Field(None, description="Model override for this agent")
    tools: List[str] = Field(default_factory=list, description="Tool names for this agent")
    handoffs: List[str] = Field(default_factory=list, description="Handoff targets")
    reflect_on_tool_use: bool = Field(default=True, description="Whether to reflect on tool usage")


class ToolConfig(BaseModel):
    """Tool configuration"""

    name: str = Field(..., description="Tool name")
    type: ToolType = Field(..., description="Tool type")
    config: Dict[str, Any] = Field(default_factory=dict, description="Tool-specific configuration")
    authentication: Optional[Dict[str, Any]] = Field(
        None, description="Authentication configuration"
    )
    enabled: bool = Field(default=True, description="Whether tool is enabled")


class TerminationConfig(BaseModel):
    """Termination condition configuration"""

    type: TerminationType = Field(..., description="Termination type")
    config: Dict[str, Any] = Field(
        default_factory=dict, description="Termination-specific configuration"
    )

    # Common termination parameters
    max_messages: Optional[int] = Field(None, description="Maximum number of messages")
    text: Optional[str] = Field(None, description="Text to look for")
    function_name: Optional[str] = Field(None, description="Function name to trigger termination")
    target: Optional[str] = Field(None, description="Handoff target")


class MemoryConfig(BaseModel):
    """Memory configuration for agents"""

    type: str = Field(default="list", description="Memory type")
    max_messages: int = Field(default=50, description="Maximum messages to keep in memory")
    compression_enabled: bool = Field(default=True, description="Whether to compress memory")
    window_size: Optional[int] = Field(None, description="Sliding window size")


class ModelConfig(BaseModel):
    """Model configuration"""

    model: str = Field(..., description="Model name (e.g., gpt-4o)")
    api_key: Optional[str] = Field(None, description="API key for the model")
    base_url: Optional[str] = Field(None, description="Base URL for the model API")
    temperature: float = Field(default=0.7, description="Model temperature")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens")
    timeout: int = Field(default=60, description="Request timeout in seconds")


class AgentConfig(BaseModel):
    """Complete agent configuration"""

    agent_id: str = Field(..., description="Unique agent identifier")
    name: str = Field(..., description="Agent configuration name")
    description: str = Field(..., description="Agent configuration description")
    version: str = Field(default="1.0.0", description="Configuration version")

    # Model configuration
    model_config: ModelConfig = Field(..., description="Model configuration")

    # Agent definitions
    agents: List[AgentDefinition] = Field(..., description="List of agents in the team")

    # Communication configuration
    communication_type: CommunicationType = Field(
        default=CommunicationType.ROUND_ROBIN, description="Communication type"
    )
    selector_prompt: Optional[str] = Field(
        None, description="Selector prompt for selector group chat"
    )
    allow_repeated_speaker: bool = Field(default=False, description="Allow repeated speakers")

    # Tool configuration
    tools: List[ToolConfig] = Field(default_factory=list, description="Available tools")

    # Termination configuration
    termination_config: TerminationConfig = Field(..., description="Termination conditions")

    # Memory configuration
    memory_config: MemoryConfig = Field(
        default_factory=MemoryConfig, description="Memory configuration"
    )

    # Knowledge bases
    knowledge_bases: List[str] = Field(
        default_factory=list, description="Knowledge base identifiers"
    )

    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(
        default_factory=datetime.utcnow, description="Last update timestamp"
    )
    created_by: Optional[str] = Field(None, description="Creator user ID")
    tags: List[str] = Field(default_factory=list, description="Configuration tags")

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}


class AgentConfigRequest(BaseModel):
    """Request for agent configuration from platform"""

    agent_id: str = Field(..., description="Agent configuration ID")
    execution_id: str = Field(..., description="Execution ID requesting the config")
    correlation_id: str = Field(..., description="Request correlation ID")
    requested_at: datetime = Field(default_factory=datetime.utcnow, description="Request timestamp")

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}


class AgentConfigResponse(BaseModel):
    """Response containing agent configuration from platform"""

    agent_id: str = Field(..., description="Agent configuration ID")
    execution_id: str = Field(..., description="Execution ID that requested the config")
    correlation_id: str = Field(..., description="Request correlation ID")
    config: AgentConfig = Field(..., description="Agent configuration")
    success: bool = Field(default=True, description="Whether request was successful")
    error: Optional[str] = Field(None, description="Error message if failed")
    responded_at: datetime = Field(
        default_factory=datetime.utcnow, description="Response timestamp"
    )

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}
