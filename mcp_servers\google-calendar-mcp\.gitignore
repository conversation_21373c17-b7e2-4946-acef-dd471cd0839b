# Dependency directories
node_modules/
jspm_packages/

# Build outputs
build/
dist/
coverage/

# Environment variables and credentials
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
gcp-oauth.keys.json
.gcp-saved-tokens.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db