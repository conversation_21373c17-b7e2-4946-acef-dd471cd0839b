#!/usr/bin/env python3
"""
Quick Setup Verification Script
Verifies that the Agent Executor Service is properly set up and ready to run
"""

import sys
import importlib
from pathlib import Path


def check_imports():
    """Check that all required modules can be imported."""
    print("🔍 Checking imports...")
    
    required_modules = [
        "app.config.config",
        "app.models.execution",
        "app.models.agent_config", 
        "app.core.agent_executor",
        "app.core.kafka_service",
        "app.services.health_service",
        "app.utils.correlation",
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0


def check_dependencies():
    """Check that required dependencies are installed."""
    print("\n📦 Checking dependencies...")
    
    required_deps = [
        "aiokafka",
        "aiohttp", 
        "psutil",
        "pydantic",
        "pydantic_settings"
    ]
    
    missing_deps = []
    
    for dep in required_deps:
        try:
            importlib.import_module(dep)
            print(f"  ✅ {dep}")
        except ImportError:
            print(f"  ❌ {dep}")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n💡 Install missing dependencies:")
        print(f"   pip install {' '.join(missing_deps)}")
    
    return len(missing_deps) == 0


def check_configuration():
    """Check configuration loading."""
    print("\n⚙️  Checking configuration...")
    
    try:
        from app.config.config import settings
        print(f"  ✅ Configuration loaded")
        print(f"  📊 Kafka broker: {settings.kafka_bootstrap_servers}")
        print(f"  🏥 Health port: {settings.health_check_port}")
        print(f"  🔄 Max executions: {settings.max_concurrent_executions}")
        return True
    except Exception as e:
        print(f"  ❌ Configuration failed: {e}")
        return False


def check_file_structure():
    """Check that required files exist."""
    print("\n📁 Checking file structure...")
    
    required_files = [
        "app/__init__.py",
        "app/main.py",
        "app/config/config.py",
        "app/core/agent_executor.py",
        "app/core/kafka_service.py",
        "app/models/execution.py",
        "app/services/health_service.py",
        "pyproject.toml",
        "Dockerfile",
        ".env.example"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0


def check_basic_functionality():
    """Test basic functionality."""
    print("\n🧪 Testing basic functionality...")
    
    try:
        from app.models.execution import AgentExecutionRequest, ExecutionStatus
        from app.services.health_service import HealthService
        
        # Test model creation
        request = AgentExecutionRequest(
            execution_id="verify-001",
            agent_id="test-agent",
            user_id="test-user",
            task="Verification test",
            correlation_id="verify-corr",
            source_service="verify-script"
        )
        print(f"  ✅ Model creation: {request.execution_id}")
        
        # Test health service
        health_service = HealthService()
        print(f"  ✅ Health service initialized")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Basic functionality test failed: {e}")
        return False


def main():
    """Run all verification checks."""
    print("🚀 Agent Executor Service - Setup Verification")
    print("=" * 50)
    
    checks = [
        ("File Structure", check_file_structure),
        ("Dependencies", check_dependencies), 
        ("Imports", check_imports),
        ("Configuration", check_configuration),
        ("Basic Functionality", check_basic_functionality)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"  ❌ {check_name} check failed with exception: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n📋 Verification Summary:")
    print("-" * 30)
    
    all_passed = True
    for check_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {status} {check_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 All checks passed! Agent Executor Service is ready to run.")
        print("\n🚀 To start the service:")
        print("   python -m app.main")
        print("\n🧪 To run Phase 1 tests:")
        print("   python test_phase1.py")
        print("\n🏥 Health check will be available at:")
        print("   http://localhost:8080/health")
        return True
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        print("\n💡 Common solutions:")
        print("   - Install dependencies: pip install aiokafka aiohttp psutil")
        print("   - Copy .env.example to .env")
        print("   - Check file permissions")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
