# execution.py
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field


class ExecutionStatus(str, Enum):
    """Execution status enumeration"""

    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class ExecutionPriority(int, Enum):
    """Execution priority levels"""

    LOW = 1
    NORMAL = 5
    HIGH = 10


class AgentExecutionRequest(BaseModel):
    """Request model for agent execution"""

    execution_id: str = Field(..., description="Unique execution identifier")
    agent_id: str = Field(..., description="Agent configuration ID")
    user_id: str = Field(..., description="User identifier")
    task: str = Field(..., description="Task to execute")
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Additional execution parameters"
    )
    priority: int = Field(default=5, ge=1, le=10, description="Execution priority (1-10)")
    timeout: int = Field(default=300, ge=30, le=3600, description="Execution timeout in seconds")
    callback_url: Optional[str] = Field(None, description="Optional callback URL for notifications")
    correlation_id: str = Field(..., description="Request correlation ID")
    source_service: str = Field(..., description="Requesting service identifier")
    created_at: datetime = Field(
        default_factory=datetime.utcnow, description="Request creation timestamp"
    )

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}


class AgentExecutionResponse(BaseModel):
    """Response model for agent execution"""

    execution_id: str = Field(..., description="Execution identifier")
    correlation_id: str = Field(..., description="Request correlation ID")
    status: ExecutionStatus = Field(..., description="Execution status")
    result: Optional[Dict[str, Any]] = Field(None, description="Execution result")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    resource_usage: Optional[Dict[str, Any]] = Field(None, description="Resource usage metrics")
    session_id: Optional[str] = Field(None, description="Agent session ID")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="Execution messages")
    completed_at: datetime = Field(
        default_factory=datetime.utcnow, description="Completion timestamp"
    )

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}


class AgentExecutionStatus(BaseModel):
    """Status update model for agent execution"""

    execution_id: str = Field(..., description="Execution identifier")
    status: ExecutionStatus = Field(..., description="Current status")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Execution progress (0.0-1.0)")
    current_step: Optional[str] = Field(None, description="Current execution step")
    message: Optional[str] = Field(None, description="Status message")
    updated_at: datetime = Field(
        default_factory=datetime.utcnow, description="Status update timestamp"
    )

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}


class ResourceUsage(BaseModel):
    """Resource usage metrics"""

    cpu_percent: float = Field(..., description="CPU usage percentage")
    memory_mb: float = Field(..., description="Memory usage in MB")
    execution_time: float = Field(..., description="Execution time in seconds")
    tokens_used: Optional[int] = Field(None, description="Number of tokens used")
    api_calls: Optional[int] = Field(None, description="Number of API calls made")


class ExecutionMetrics(BaseModel):
    """Execution metrics for monitoring"""

    execution_id: str = Field(..., description="Execution identifier")
    agent_id: str = Field(..., description="Agent configuration ID")
    user_id: str = Field(..., description="User identifier")
    status: ExecutionStatus = Field(..., description="Execution status")
    started_at: datetime = Field(..., description="Execution start time")
    completed_at: Optional[datetime] = Field(None, description="Execution completion time")
    execution_time: Optional[float] = Field(None, description="Total execution time")
    resource_usage: Optional[ResourceUsage] = Field(None, description="Resource usage")
    error_type: Optional[str] = Field(None, description="Error type if failed")

    model_config = {"json_encoders": {datetime: lambda v: v.isoformat()}}
