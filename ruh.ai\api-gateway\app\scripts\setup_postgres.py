"""
<PERSON><PERSON><PERSON> to set up the PostgreSQL database for the API Gateway.

This script:
1. Creates the database if it doesn't exist
2. Creates the tables in the database

Usage:
    poetry run python -m app.scripts.setup_postgres

Environment variables:
    DB_HOST - PostgreSQL host
    DB_PORT - PostgreSQL port
    DB_USER - PostgreSQL user
    DB_PASSWORD - PostgreSQL password
    DB_NAME - PostgreSQL database name
"""

import sys
import os
from pathlib import Path
import logging

# Add the parent directory to sys.path
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.db.models import Base, OAuthCredential


def create_database():
    """Create the PostgreSQL database if it doesn't exist."""
    # Connect to the default 'postgres' database to create our application database
    postgres_url = f"postgresql://{settings.DB_USER}:{settings.DB_PASSWORD}@{settings.DB_HOST}:{settings.DB_PORT}/postgres"

    try:
        # Create a connection to the postgres database
        engine = create_engine(postgres_url)
        conn = engine.connect()
        conn.execute(text("COMMIT"))  # Close any open transactions

        # Check if our database exists
        result = conn.execute(
            text(f"SELECT 1 FROM pg_database WHERE datname = '{settings.DB_NAME}'")
        )
        exists = result.scalar() == 1

        if not exists:
            logger.info(f"Creating database '{settings.DB_NAME}'...")
            # Close any open connections to the postgres database
            conn.execute(text("COMMIT"))
            # Create the database
            conn.execute(text(f"CREATE DATABASE {settings.DB_NAME}"))
            logger.info(f"Database '{settings.DB_NAME}' created successfully")
        else:
            logger.info(f"Database '{settings.DB_NAME}' already exists")

        conn.close()
        engine.dispose()
        return True
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False


def create_tables():
    """Create the tables in the PostgreSQL database."""
    try:
        # Connect to our application database
        db_url = str(settings.SQLALCHEMY_DATABASE_URI)
        engine = create_engine(db_url)

        # Create tables
        Base.metadata.create_all(engine)
        logger.info("Tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        return False


def main():
    """Main function to set up the PostgreSQL database."""
    logger.info("Setting up PostgreSQL database...")

    # Check if PostgreSQL settings are configured
    if not settings.DB_HOST or not settings.DB_PORT or not settings.DB_USER or not settings.DB_NAME:
        logger.error(
            "PostgreSQL settings are not configured. Please set DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, and DB_NAME environment variables."
        )
        return False

    # Create database
    if not create_database():
        return False

    # Create tables
    if not create_tables():
        return False

    logger.info("PostgreSQL setup completed successfully")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
