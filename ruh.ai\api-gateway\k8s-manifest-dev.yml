apiVersion: v1
kind: ServiceAccount
metadata:
  name: gateway-service-ai-sa
  namespace: ruh-dev
  labels:
    name: gateway-service-ai-sa
    namespace: ruh-dev
    app: gateway-service-ai
    deployment: gateway-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway-service-ai-dp
  namespace: ruh-dev
  labels:
    name: gateway-service-ai-dp
    namespace: ruh-dev
    app: gateway-service-ai
    serviceaccount: gateway-service-ai-sa
    deployment: gateway-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gateway-service-ai
      deployment: gateway-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-dev
        app: gateway-service-ai
        deployment: gateway-service-ai-dp
    spec:
      serviceAccountName: gateway-service-ai-sa      
      containers:
      - name: gateway-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50059
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: gateway-service-ai-svc
  namespace: ruh-dev
spec:
  selector:
    app: gateway-service-ai
    deployment: gateway-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50059
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:gateway-service-user-hpa
#   namespace: ruh-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:gateway-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-service-user-ingress
  namespace: ruh-dev
spec:
  ingressClassName: nginx
  rules:
  - host: gateway-api-ruh-dev.rapidinnovation.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway-service-ai-svc
            port:
              number: 80




