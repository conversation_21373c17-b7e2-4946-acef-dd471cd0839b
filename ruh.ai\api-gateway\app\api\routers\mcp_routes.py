from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from app.services.mcp_service import MCPServiceClient
from app.schemas.mcp import (
    MCPPatchPayload,
    MCPsByIdsRequest,
    MCPsByIdsResponse,
    PaginatedMCPResponse,
    PaginationMetadata,
    StatusEnum,
    UpdateMCPResponse,
    MCPResponse,
    MCPCreate,
    MCPInDB,
    CreateMCPResponse,
    DeleteMCPResponse,
    UrlTypeEnum,
    VisibilityEnum,
    McpDepartment,
    MCPUrlsRequest,
    MCPUrlItem,
    MCPUrlsResponse,
)
from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.core.security import (
    validate_server_auth_key,
    validate_agent_platform_auth_key,
    validate_workflow_service_auth_key,
)
from app.services.user_service import UserServiceClient
from app.utils.parse_error import parse_error
import json

mcp_router = APIRouter(prefix="/mcps", tags=["mcps"])
mcp_marketplace = APIRouter(prefix="/marketplace", tags=["mcps"])
mcp_service = MCPServiceClient()
user_service = UserServiceClient()


@mcp_router.post("", response_model=CreateMCPResponse)
async def create_mcp(
    mcp_data: MCPCreate, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Creates a new MCP.
    """
    try:
        validate_response = await user_service.validate_user(current_user["user_id"])
        if not validate_response["success"]:
            raise HTTPException(status_code=400, detail=validate_response["message"])

        user_details = validate_response["user"]

        print(f"[DEBUG API] Owner details from user_service: {user_details}")

        response = await mcp_service.createMCP(
            logo=mcp_data.logo,
            name=mcp_data.name,
            description=mcp_data.description,
            owner_details=user_details,
            owner_type=current_user["role"],
            git_url=mcp_data.git_url,
            url=mcp_data.url,
            url_type=mcp_data.url_type,
            department=mcp_data.department,
            visibility=mcp_data.visibility,
            tags=mcp_data.tags,
            status=mcp_data.status,
            user_ids=mcp_data.user_ids,
        )

        return CreateMCPResponse(
            success=response.success,
            message=response.message,
            mcp_id=response.mcp.id if hasattr(response, "mcp") and response.mcp else None,
        )
    except Exception as e:
        print(f"[DEBUG] Error in create_mcp: {str(e)}")
        error_details = parse_error(str(e))  # Your existing error parsing
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("/{mcp_id}", response_model=MCPResponse)
async def get_mcp(mcp_id: str, current_user: dict = Depends(role_required(["user", "admin"]))):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(
            success=response.success,
            message=response.message,
            mcp=MCPInDB(**mcp_dict),  # Use MCPInDB to parse the dictionary
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.patch("/{mcp_id}", response_model=UpdateMCPResponse)
async def update_mcp(
    mcp_id: str,
    payload: MCPPatchPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Partially updates an MCP by ID.
    Only the fields provided in the request body will be updated.
    """
    try:
        validate_response = await user_service.validate_user(
            current_user["user_id"]
        )  # Assuming user_service exists
        if not validate_response.get("success"):
            raise HTTPException(
                status_code=400, detail=validate_response.get("message", "User validation failed")
            )

        user_details = validate_response["user"]

        update_data = payload.model_dump(exclude_unset=True)

        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update.")

        response = await mcp_service.updateMCP(
            mcp_id=mcp_id, update_fields=update_data, current_owner_details=user_details
        )

        if not response.success:
            raise HTTPException(status_code=400, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)
        return UpdateMCPResponse(
            success=response.success, message=response.message, mcp=MCPInDB(**mcp_dict)
        )
    except Exception as e:
        print(f"[DEBUG] Error in patch_mcp: {str(e)}")
        error_details = parse_error(str(e))  # Your existing error parser
        raise HTTPException(
            status_code=error_details.get("code", 500),
            detail=error_details.get("message", "An unexpected error occurred."),
        )


@mcp_router.delete("/{mcp_id}", response_model=DeleteMCPResponse)
async def delete_mcp(mcp_id: str, current_user: dict = Depends(role_required(["user", "admin"]))):
    """
    Deletes an MCP by ID.

    This endpoint deletes an MCP by its ID.
    """
    try:
        response = await mcp_service.deleteMCP(mcp_id)

        return DeleteMCPResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get("", response_model=PaginatedMCPResponse, description="get MCPs for admin & user")
async def list_mcps(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
    department: Optional[McpDepartment] = Query(None, description="Filter by department"),
    visibility: Optional[VisibilityEnum] = Query(None, description="Filter by visibility"),
    status: Optional[StatusEnum] = Query(None, description="Filter by status"),
    url_type: Optional[UrlTypeEnum] = Query(None, description="Filter by URL type"),
    current_user: dict = Depends(role_required(["user", "admin"])),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
):
    """
    Retrieves a paginated list of MCPs, with optional filters.
    - Admins can see all MCPs (subject to filters).
    - Users can only see their own MCPs (subject to filters).
    """
    try:
        print(
            f"[DEBUG] MCPs list request: page={page}, page_size={page_size}, department={department}, visibility={visibility}, status={status}, url_type={url_type}, tags={tags}"
        )
        owner_id_filter: Optional[str] = None
        if current_user.get("role") == "user":
            owner_id_filter = current_user.get("user_id")
            if not owner_id_filter:
                raise HTTPException(status_code=403, detail="User ID not found for user role.")

        # The gRPC client expects Python enums (or their .value) which it maps to proto enums
        response = await mcp_service.listMCPS(
            page=page,
            page_size=page_size,
            owner_id=owner_id_filter,
            department=department.value if department else None,
            visibility=visibility,
            status=status,
            url_type=url_type,
            tags=tags,
        )

        print("[DEBUG] Response: ", response)
        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)

            mcps.append(MCPInDB.parse_obj(mcp_dict))

        print("[DEBUG] Returning mcps: ", mcps)
        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=response.total,
            totalPages=response.total_pages,
            currentPage=response.page,
            pageSize=page_size,
            hasNextPage=response.page < response.total_pages,
            hasPreviousPage=response.page > 1,
        )

        return PaginatedMCPResponse(data=mcps, metadata=metadata)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/agent-platform/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_mcp_for_agent_platform(
    mcp_id: str,
):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/orchestration/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_mcp_for_orchestration(mcp_id: str):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.get(
    "/workflow-service/{mcp_id}",
    response_model=MCPResponse,
    dependencies=[Depends(validate_workflow_service_auth_key)],
)
async def get_mcp_for_workflow_service(mcp_id: str):
    """
    Gets an MCP by ID.

    This endpoint retrieves an MCP by its ID.
    """
    try:
        response = await mcp_service.getMCPById(mcp_id)
        print(f"[DEBUG] MCP response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        mcp_dict = MessageToDict(response.mcp, preserving_proto_field_name=True)

        # Parse JSON string tags to dict if it's a string
        if isinstance(mcp_dict.get("tags"), str):
            mcp_dict["tags"] = json.loads(mcp_dict["tags"])

        print(f"[DEBUG] MCP data converted to dict: {mcp_dict}")
        return MCPResponse(success=response.success, message=response.message, mcp=mcp_dict)
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error details: {error_details}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post("/by-ids", response_model=MCPsByIdsResponse)
async def get_mcps_by_ids(
    request: MCPsByIdsRequest, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Retrieves multiple MCPs by their IDs.

    This endpoint allows fetching multiple MCPs in a single request by providing a list of MCP IDs.

    ## Request Body
    - **ids**: List of MCP IDs to retrieve

    ## Response
    Returns a list of MCPs matching the provided IDs and the total count.

    ## Example
    ```
    POST /mcps/by-ids
    {
        "ids": ["mcp-id-1", "mcp-id-2", "mcp-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")

        print(f"[DEBUG] Fetching MCPs by IDs: {request.ids}")
        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)

        mcps = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            mcps.append(MCPInDB.model_validate(mcp_dict))

        return MCPsByIdsResponse(
            success=True, message=f"Retrieved {len(mcps)} MCPs", mcps=mcps, total=len(mcps)
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_mcps_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@mcp_router.post(
    "/orchestration/urls",
    response_model=MCPUrlsResponse,
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_mcps_urls_for_orchestration(request: MCPUrlsRequest):
    """
    Bulk fetches the `url` field for multiple MCPs by their IDs.
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No MCP IDs provided")
        response = await mcp_service.getMCPsByIds(mcp_ids=request.ids)
        urls = []
        for mcp in response.mcps:
            mcp_dict = MessageToDict(mcp, preserving_proto_field_name=True)
            urls.append(MCPUrlItem(mcp_id=mcp_dict.get("id"), url=mcp_dict.get("url")))
        return MCPUrlsResponse(success=True, message=f"Retrieved {len(urls)} MCP urls", urls=urls)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
