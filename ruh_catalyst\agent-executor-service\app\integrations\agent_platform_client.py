# agent_platform_client.py
import asyncio
import json
import logging
import time
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from app.config.config import settings
from app.models.agent_config import AgentConfig, AgentConfigRequest, AgentConfigResponse
from app.utils.correlation import CorrelationTracker


class AgentPlatformClientError(Exception):
    """Custom exception for agent platform client errors."""
    pass


class AgentPlatformClient:
    """
    Client for communicating with the agent platform via Kafka.
    Handles configuration requests and response correlation.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.kafka_broker = settings.kafka_bootstrap_servers
        self.config_request_topic = settings.kafka_config_request_topic
        self.config_response_topic = settings.kafka_config_response_topic
        self.consumer_group_id = f"{settings.kafka_consumer_group_id}_config_client"
        
        # Configuration caching
        self.cache_ttl = settings.config_cache_ttl
        self._config_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, float] = {}
        
        # Correlation tracking for request/response matching
        self.correlation_tracker = CorrelationTracker(timeout_seconds=60)
        
        # Kafka clients
        self.producer: Optional[AIOKafkaProducer] = None
        self.consumer: Optional[AIOKafkaConsumer] = None
        self._consumer_task: Optional[asyncio.Task] = None
        self._running = False
        
        self.logger.info(f"AgentPlatformClient initialized with broker: {self.kafka_broker}")

    async def start(self):
        """Start the Kafka producer and consumer."""
        if self._running:
            return
            
        try:
            # Initialize producer
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.kafka_broker,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
            await self.producer.start()
            
            # Initialize consumer for config responses
            self.consumer = AIOKafkaConsumer(
                self.config_response_topic,
                bootstrap_servers=self.kafka_broker,
                group_id=self.consumer_group_id,
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await self.consumer.start()
            
            # Start response consumer task
            self._consumer_task = asyncio.create_task(self._consume_responses())
            self._running = True
            
            self.logger.info("AgentPlatformClient started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start AgentPlatformClient: {e}", exc_info=True)
            await self.stop()
            raise AgentPlatformClientError(f"Failed to start client: {e}")

    async def stop(self):
        """Stop the Kafka producer and consumer."""
        self._running = False
        
        if self._consumer_task:
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                pass
        
        if self.consumer:
            try:
                await self.consumer.stop()
            except Exception as e:
                self.logger.error(f"Error stopping consumer: {e}")
        
        if self.producer:
            try:
                await self.producer.stop()
            except Exception as e:
                self.logger.error(f"Error stopping producer: {e}")
        
        self.logger.info("AgentPlatformClient stopped")

    async def get_agent_config(
        self, 
        agent_id: str, 
        execution_id: str,
        correlation_id: str,
        use_cache: bool = True
    ) -> AgentConfig:
        """
        Get agent configuration from the platform.
        
        Args:
            agent_id: The agent configuration ID
            execution_id: The execution ID requesting the config
            correlation_id: Request correlation ID
            use_cache: Whether to use cached configuration
            
        Returns:
            AgentConfig: The agent configuration
            
        Raises:
            AgentPlatformClientError: If configuration cannot be retrieved
        """
        self.logger.info(f"Requesting agent config for {agent_id} (execution: {execution_id})")
        
        # Check cache first
        if use_cache:
            cached_config = self._get_from_cache(agent_id)
            if cached_config:
                self.logger.info(f"Using cached config for {agent_id}")
                return AgentConfig.parse_obj(cached_config)
        
        if not self._running:
            raise AgentPlatformClientError("Client not started")
        
        # Create config request
        request = AgentConfigRequest(
            agent_id=agent_id,
            execution_id=execution_id,
            correlation_id=correlation_id
        )
        
        # Track the request for response correlation
        self.correlation_tracker.track_request(
            correlation_id, 
            {
                "agent_id": agent_id,
                "execution_id": execution_id,
                "request_time": datetime.utcnow().isoformat()
            }
        )
        
        try:
            # Send request to agent platform
            headers = [("correlationId", correlation_id.encode("utf-8"))]
            await self.producer.send_and_wait(
                self.config_request_topic,
                value=request.dict(),
                headers=headers
            )
            
            self.logger.debug(f"Sent config request for {agent_id}")
            
            # Wait for response with timeout
            config = await self._wait_for_config_response(correlation_id, timeout=30.0)
            
            # Cache the configuration
            if use_cache:
                self._store_in_cache(agent_id, config.dict())
            
            return config
            
        except Exception as e:
            self.correlation_tracker.remove_request(correlation_id)
            self.logger.error(f"Failed to get config for {agent_id}: {e}", exc_info=True)
            raise AgentPlatformClientError(f"Failed to get agent config: {e}")

    async def _wait_for_config_response(self, correlation_id: str, timeout: float) -> AgentConfig:
        """Wait for configuration response with timeout."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # Check if we have a response
            request_info = self.correlation_tracker.get_request(correlation_id)
            if request_info and "response" in request_info:
                response_data = request_info["response"]
                self.correlation_tracker.remove_request(correlation_id)
                
                if response_data.get("success"):
                    return AgentConfig.parse_obj(response_data["config"])
                else:
                    error_msg = response_data.get("error", "Unknown error")
                    raise AgentPlatformClientError(f"Platform returned error: {error_msg}")
            
            await asyncio.sleep(0.1)  # Check every 100ms
        
        # Timeout reached
        self.correlation_tracker.remove_request(correlation_id)
        raise AgentPlatformClientError(f"Timeout waiting for config response: {correlation_id}")

    async def _consume_responses(self):
        """Consume configuration responses from Kafka."""
        self.logger.info("Starting config response consumer")
        
        try:
            async for msg in self.consumer:
                try:
                    # Decode message
                    response_data = json.loads(msg.value.decode("utf-8"))
                    correlation_id = response_data.get("correlation_id")
                    
                    if not correlation_id:
                        self.logger.warning("Received response without correlation_id")
                        continue
                    
                    self.logger.debug(f"Received config response for {correlation_id}")
                    
                    # Find the tracked request and add response
                    request_info = self.correlation_tracker.get_request(correlation_id)
                    if request_info:
                        request_info["response"] = response_data
                        self.logger.debug(f"Matched response to request {correlation_id}")
                    else:
                        self.logger.warning(f"No tracked request found for {correlation_id}")
                        
                except Exception as e:
                    self.logger.error(f"Error processing config response: {e}", exc_info=True)
                    
        except asyncio.CancelledError:
            self.logger.info("Config response consumer cancelled")
        except Exception as e:
            self.logger.error(f"Error in config response consumer: {e}", exc_info=True)

    def _is_cache_valid(self, agent_id: str) -> bool:
        """Check if cached configuration is still valid."""
        if agent_id not in self._cache_timestamps:
            return False
        
        elapsed = time.time() - self._cache_timestamps[agent_id]
        return elapsed < self.cache_ttl

    def _get_from_cache(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve configuration from cache if valid."""
        if self._is_cache_valid(agent_id):
            self.logger.debug(f"Cache hit for agent {agent_id}")
            return self._config_cache.get(agent_id)
        else:
            # Clean up expired cache entry
            if agent_id in self._config_cache:
                del self._config_cache[agent_id]
                del self._cache_timestamps[agent_id]
                self.logger.debug(f"Expired cache entry removed for {agent_id}")
            return None

    def _store_in_cache(self, agent_id: str, config: Dict[str, Any]) -> None:
        """Store configuration in cache with current timestamp."""
        self._config_cache[agent_id] = config
        self._cache_timestamps[agent_id] = time.time()
        self.logger.debug(f"Configuration cached for {agent_id}")

    def clear_cache(self, agent_id: Optional[str] = None) -> None:
        """Clear cached configurations."""
        if agent_id:
            if agent_id in self._config_cache:
                del self._config_cache[agent_id]
                del self._cache_timestamps[agent_id]
                self.logger.info(f"Cleared cache for {agent_id}")
        else:
            self._config_cache.clear()
            self._cache_timestamps.clear()
            self.logger.info("Cleared all cached configurations")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        valid_entries = sum(1 for agent_id in self._config_cache.keys() if self._is_cache_valid(agent_id))
        return {
            "total_entries": len(self._config_cache),
            "valid_entries": valid_entries,
            "expired_entries": len(self._config_cache) - valid_entries,
            "cache_ttl": self.cache_ttl
        }
