from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

try:
    # Convert PostgresDsn to string
    db_uri = str(settings.SQLALCHEMY_DATABASE_URI)
    engine = create_engine(db_uri)
    logger.info("Connected to PostgreSQL database")
except Exception as e:
    logger.error(f"Error connecting to PostgreSQL: {e}")
    raise RuntimeError(f"Failed to connect to PostgreSQL database: {e}")

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()


# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
