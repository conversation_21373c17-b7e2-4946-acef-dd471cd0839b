# Agent Executor Service - Architecture Design

## Overview

The Agent Executor Service is designed as the centralized execution engine for all agents across the platform, following the proven patterns from the mcp-executor-service. This service will handle agent instantiation, execution orchestration, session management, and resource monitoring while communicating with the agent platform for configuration and tool definitions.

## Core Architecture

### Service Architecture Pattern

Following the mcp-executor-service pattern, the agent executor service implements:

```python
# Core service structure similar to mcp-executor-service
class AgentExecutorService:
    def __init__(self):
        self.kafka_service = KafkaService()
        self.execution_engine = ExecutionEngine()
        self.session_manager = SessionManager()
        self.resource_manager = ResourceManager()
        self.monitoring_service = MonitoringService()
    
    async def start(self):
        """Start all service components"""
        await self.kafka_service.start_consumer()
        await self.execution_engine.initialize()
        await self.session_manager.initialize()
        
    async def process_execution_request(self, request: AgentExecutionRequest):
        """Main execution processing logic"""
        # Similar to mcp-executor-service's execute_tool method
        pass
```

### Component Architecture

```mermaid
graph TB
    subgraph "Agent Executor Service"
        KS[Kafka Service]
        EE[Execution Engine]
        SM[Session Manager]
        RM[Resource Manager]
        MS[Monitoring Service]
        
        subgraph "Execution Core"
            AE[AutoGen Executor]
            TE[Tool Executor]
            SP[Stream Processor]
        end
        
        subgraph "Integration Layer"
            APC[Agent Platform Client]
            MCP[MCP Client]
            WFC[Workflow Client]
            RC[Redis Client]
        end
    end
    
    subgraph "External Services"
        AP[Agent Platform]
        MCPS[MCP Servers]
        WE[Workflow Engine]
        RD[Redis]
        KF[Kafka]
    end
    
    KS --> KF
    EE --> AE
    EE --> TE
    EE --> SP
    SM --> RC
    SM --> RD
    APC --> AP
    MCP --> MCPS
    WFC --> WE
```

## Core Components

### 1. Kafka Service (`app/core/kafka_service.py`)

Based on mcp-executor-service pattern:

```python
class KafkaService:
    def __init__(self):
        self.consumer = AIOKafkaConsumer(
            settings.kafka_execution_request_topic,
            bootstrap_servers=settings.kafka_bootstrap_servers,
            group_id=settings.kafka_consumer_group_id,
            auto_offset_reset="latest",
            enable_auto_commit=False,
        )
        self.producer = AIOKafkaProducer(
            bootstrap_servers=settings.kafka_bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )
        self.semaphore = asyncio.Semaphore(settings.max_concurrent_executions)
    
    async def start_consumer(self):
        """Start consumer loop similar to mcp-executor-service"""
        while True:
            try:
                await self.consumer.start()
                await self.producer.start()
                
                async for msg in self.consumer:
                    await self.semaphore.acquire()
                    asyncio.create_task(
                        self.process_message(msg, self.semaphore)
                    )
            except KafkaError as e:
                logger.error(f"Kafka error: {e}")
                await asyncio.sleep(5)
    
    async def process_message(self, msg, semaphore):
        """Process execution request message"""
        semaphore.release()  # Release early like mcp-executor-service
        
        try:
            request = AgentExecutionRequest.parse_raw(msg.value)
            result = await self.execution_engine.execute_agent(request)
            await self.send_response(request.correlation_id, result)
        except Exception as e:
            await self.send_error_response(
                request.correlation_id, str(e)
            )
        finally:
            await self.consumer.commit()
```

### 2. Execution Engine (`app/core/execution_engine.py`)

Core execution orchestration following AutoGen 0.5.7 patterns:

```python
class ExecutionEngine:
    def __init__(self):
        self.session_manager = SessionManager()
        self.tool_executor = ToolExecutor()
        self.agent_platform_client = AgentPlatformClient()
        
    async def execute_agent(
        self, 
        request: AgentExecutionRequest
    ) -> AgentExecutionResult:
        """Main execution method"""
        
        # 1. Get agent configuration from platform
        config = await self.agent_platform_client.get_agent_config(
            request.agent_id, request.correlation_id
        )
        
        # 2. Create or get session
        session = await self.session_manager.get_or_create_session(
            request.execution_id, config
        )
        
        # 3. Initialize AutoGen agents and team
        agents, team = await self.create_autogen_team(config, session)
        
        # 4. Execute with streaming
        result = await self.execute_with_streaming(
            team, request.task, request.execution_id
        )
        
        # 5. Update session and cleanup
        await self.session_manager.update_session(
            request.execution_id, result
        )
        
        return result
    
    async def create_autogen_team(
        self, 
        config: AgentConfig, 
        session: ExecutionSession
    ) -> Tuple[List[AssistantAgent], RoundRobinGroupChat]:
        """Create AutoGen team based on configuration"""
        
        # Load tools
        tools = await self.tool_executor.load_tools(config.tools)
        
        # Create model client
        model_client = OpenAIChatCompletionClient(
            model=config.model,
            api_key=config.api_key
        )
        
        # Create agents
        agents = []
        for agent_config in config.agents:
            agent = AssistantAgent(
                name=agent_config.name,
                description=agent_config.description,
                model_client=model_client,
                system_message=agent_config.system_message,
                tools=tools,
                memory=session.memory
            )
            agents.append(agent)
        
        # Create team based on communication type
        if config.communication_type == "round_robin":
            team = RoundRobinGroupChat(
                participants=agents,
                termination_condition=self.create_termination_condition(config)
            )
        elif config.communication_type == "selector":
            team = SelectorGroupChat(
                participants=agents,
                model_client=model_client,
                termination_condition=self.create_termination_condition(config)
            )
        
        return agents, team
    
    async def execute_with_streaming(
        self,
        team: RoundRobinGroupChat,
        task: str,
        execution_id: str
    ) -> AgentExecutionResult:
        """Execute team with streaming response processing"""
        
        start_time = time.time()
        messages = []
        
        try:
            async for response in team.run_stream(task=task):
                if isinstance(response, TaskResult):
                    # Final result
                    execution_time = time.time() - start_time
                    return AgentExecutionResult(
                        execution_id=execution_id,
                        status=ExecutionStatus.COMPLETED,
                        result=response.messages[-1].content,
                        messages=messages,
                        execution_time=execution_time
                    )
                else:
                    # Intermediate message
                    message_data = {
                        "source": response.source,
                        "content": response.content,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    messages.append(message_data)
                    
                    # Send streaming update
                    await self.send_streaming_update(execution_id, message_data)
                    
        except Exception as e:
            execution_time = time.time() - start_time
            return AgentExecutionResult(
                execution_id=execution_id,
                status=ExecutionStatus.FAILED,
                error=str(e),
                messages=messages,
                execution_time=execution_time
            )
```

### 3. Session Manager (`app/core/session_manager.py`)

Migrated from agent platform with enhancements:

```python
class SessionManager:
    def __init__(self):
        self.redis_client = redis.Redis.from_url(settings.redis_url)
        self.compression_enabled = settings.session_compression_enabled
        
    async def get_or_create_session(
        self, 
        execution_id: str, 
        config: AgentConfig
    ) -> ExecutionSession:
        """Get existing session or create new one"""
        
        session_key = f"session:{execution_id}"
        session_data = await self.redis_client.get(session_key)
        
        if session_data:
            # Deserialize existing session
            if self.compression_enabled:
                session_data = gzip.decompress(session_data)
            session_dict = json.loads(session_data)
            return ExecutionSession.parse_obj(session_dict)
        else:
            # Create new session
            session = ExecutionSession(
                execution_id=execution_id,
                agent_config=config,
                created_at=datetime.utcnow(),
                memory=ListMemory(),
                status=ExecutionStatus.PENDING
            )
            await self.save_session(session)
            return session
    
    async def save_session(self, session: ExecutionSession):
        """Save session to Redis with compression"""
        
        session_key = f"session:{session.execution_id}"
        session_data = session.json().encode('utf-8')
        
        if self.compression_enabled:
            session_data = gzip.compress(session_data)
            
        await self.redis_client.setex(
            session_key, 
            settings.session_ttl_seconds, 
            session_data
        )
    
    async def cleanup_expired_sessions(self):
        """Background task to cleanup expired sessions"""
        # Implementation similar to agent platform
        pass
```

### 4. Tool Executor (`app/core/tool_executor.py`)

Migrated tool loading and execution logic:

```python
class ToolExecutor:
    def __init__(self):
        self.mcp_client = MCPClient()
        self.workflow_client = WorkflowClient()
        self.dynamic_tool_cache = {}
        
    async def load_tools(self, tool_configs: List[ToolConfig]) -> List[Tool]:
        """Load tools based on configuration"""
        
        tools = []
        for tool_config in tool_configs:
            if tool_config.type == "mcp":
                tool = await self.load_mcp_tool(tool_config)
            elif tool_config.type == "workflow":
                tool = await self.load_workflow_tool(tool_config)
            elif tool_config.type == "dynamic":
                tool = await self.load_dynamic_tool(tool_config)
            
            if tool:
                tools.append(tool)
                
        return tools
    
    async def load_mcp_tool(self, config: ToolConfig) -> Tool:
        """Load MCP tool similar to agent platform implementation"""
        # Migrate from app/tools/mcp_tool_loader.py
        pass
    
    async def load_workflow_tool(self, config: ToolConfig) -> Tool:
        """Load workflow tool"""
        # Migrate from app/tools/workflow_tool_loader.py
        pass
    
    async def load_dynamic_tool(self, config: ToolConfig) -> Tool:
        """Load dynamic API tool"""
        # Migrate from app/tools/dynamic_tool_loader.py
        pass
```

## Integration Patterns

### 1. Agent Platform Communication

```python
class AgentPlatformClient:
    def __init__(self):
        self.kafka_producer = AIOKafkaProducer(...)
        self.response_cache = {}
        
    async def get_agent_config(
        self, 
        agent_id: str, 
        correlation_id: str
    ) -> AgentConfig:
        """Request agent configuration from platform"""
        
        # Check cache first
        cache_key = f"config:{agent_id}"
        if cache_key in self.response_cache:
            return self.response_cache[cache_key]
        
        # Send request via Kafka
        request = AgentConfigRequest(
            agent_id=agent_id,
            correlation_id=correlation_id
        )
        
        await self.kafka_producer.send(
            "agent-config-requests",
            value=request.dict()
        )
        
        # Wait for response (with timeout)
        response = await self.wait_for_response(correlation_id, timeout=30)
        
        # Cache response
        self.response_cache[cache_key] = response.config
        
        return response.config
```

### 2. Kafka Message Handling

Following mcp-executor-service patterns:

```python
# Message processing with correlation ID tracking
async def process_execution_request(self, msg):
    """Process execution request with proper error handling"""
    
    try:
        # Parse request
        request_data = json.loads(msg.value.decode('utf-8'))
        request = AgentExecutionRequest.parse_obj(request_data)
        
        # Extract headers
        headers = self.decode_headers(msg.headers)
        correlation_id = headers.get("correlationId")
        reply_topic = headers.get("reply-topic")
        
        # Execute agent
        result = await self.execution_engine.execute_agent(request)
        
        # Send response
        await self.send_response(reply_topic, correlation_id, result)
        
    except Exception as e:
        await self.send_error_response(
            reply_topic, correlation_id, str(e)
        )
    finally:
        await self.consumer.commit()
```

## Data Models

### Execution Models

```python
class AgentExecutionRequest(BaseModel):
    execution_id: str
    agent_id: str
    user_id: str
    task: str
    parameters: Dict[str, Any] = {}
    priority: int = 5
    timeout: int = 300
    correlation_id: str
    source_service: str
    created_at: datetime

class AgentExecutionResult(BaseModel):
    execution_id: str
    status: ExecutionStatus
    result: Optional[str] = None
    error: Optional[str] = None
    messages: List[Dict[str, Any]] = []
    execution_time: float
    resource_usage: Optional[ResourceUsage] = None
    completed_at: datetime

class ExecutionSession(BaseModel):
    execution_id: str
    agent_config: AgentConfig
    memory: ListMemory
    status: ExecutionStatus
    created_at: datetime
    updated_at: Optional[datetime] = None
    metadata: Dict[str, Any] = {}
```

### Configuration Models

```python
class AgentConfig(BaseModel):
    agent_id: str
    name: str
    description: str
    model: str
    api_key: str
    system_message: str
    agents: List[AgentDefinition]
    tools: List[ToolConfig]
    communication_type: str
    termination_config: TerminationConfig
    memory_config: MemoryConfig

class ToolConfig(BaseModel):
    name: str
    type: str  # mcp, workflow, dynamic
    config: Dict[str, Any]
    authentication: Optional[Dict[str, Any]] = None
```

## Deployment Architecture

### Container Structure

```dockerfile
# Multi-stage build for agent executor service
FROM python:3.12-slim as base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY app/ ./app/

# Create non-root user
RUN useradd --create-home --shell /bin/bash executor
USER executor

EXPOSE 8000 9090

CMD ["python", "-m", "app.main"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-executor-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-executor-service
  template:
    metadata:
      labels:
        app: agent-executor-service
    spec:
      containers:
      - name: agent-executor
        image: agent-executor-service:latest
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: REDIS_URL
          value: "redis://redis:6379"
        - name: MAX_CONCURRENT_EXECUTIONS
          value: "50"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

This architecture design provides a robust, scalable foundation for the agent executor service that follows proven patterns while handling the specific requirements of agent execution and orchestration.
