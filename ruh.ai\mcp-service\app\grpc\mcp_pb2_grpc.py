# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc import mcp_pb2 as mcp__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in mcp_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class MCPServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.createMCP = channel.unary_unary(
                '/mcp.MCPService/createMCP',
                request_serializer=mcp__pb2.CreateMCPRequest.SerializeToString,
                response_deserializer=mcp__pb2.MCPResponse.FromString,
                _registered_method=True)
        self.getMCP = channel.unary_unary(
                '/mcp.MCPService/getMCP',
                request_serializer=mcp__pb2.GetMCPRequest.SerializeToString,
                response_deserializer=mcp__pb2.MCPResponse.FromString,
                _registered_method=True)
        self.updateMCP = channel.unary_unary(
                '/mcp.MCPService/updateMCP',
                request_serializer=mcp__pb2.UpdateMCPRequest.SerializeToString,
                response_deserializer=mcp__pb2.MCPResponse.FromString,
                _registered_method=True)
        self.deleteMCP = channel.unary_unary(
                '/mcp.MCPService/deleteMCP',
                request_serializer=mcp__pb2.DeleteMCPRequest.SerializeToString,
                response_deserializer=mcp__pb2.DeleteMCPResponse.FromString,
                _registered_method=True)
        self.listMCPs = channel.unary_unary(
                '/mcp.MCPService/listMCPs',
                request_serializer=mcp__pb2.ListMCPsRequest.SerializeToString,
                response_deserializer=mcp__pb2.ListMCPsResponse.FromString,
                _registered_method=True)
        self.getMCPsByIds = channel.unary_unary(
                '/mcp.MCPService/getMCPsByIds',
                request_serializer=mcp__pb2.GetMCPsByIdsRequest.SerializeToString,
                response_deserializer=mcp__pb2.ListMCPsResponse.FromString,
                _registered_method=True)
        self.getMarketplaceMCPs = channel.unary_unary(
                '/mcp.MCPService/getMarketplaceMCPs',
                request_serializer=mcp__pb2.GetMarketplaceMCPsRequest.SerializeToString,
                response_deserializer=mcp__pb2.GetMarketplaceMCPsResponse.FromString,
                _registered_method=True)
        self.getMarketplaceMCPDetail = channel.unary_unary(
                '/mcp.MCPService/getMarketplaceMCPDetail',
                request_serializer=mcp__pb2.GetMarketplaceMCPDetailRequest.SerializeToString,
                response_deserializer=mcp__pb2.GetMarketplaceMCPDetailResponse.FromString,
                _registered_method=True)
        self.rateMCP = channel.unary_unary(
                '/mcp.MCPService/rateMCP',
                request_serializer=mcp__pb2.RateMCPRequest.SerializeToString,
                response_deserializer=mcp__pb2.RateMCPResponse.FromString,
                _registered_method=True)
        self.useMCP = channel.unary_unary(
                '/mcp.MCPService/useMCP',
                request_serializer=mcp__pb2.UseMCPRequest.SerializeToString,
                response_deserializer=mcp__pb2.UseMCPResponse.FromString,
                _registered_method=True)


class MCPServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def createMCP(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMCP(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def updateMCP(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def deleteMCP(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listMCPs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMCPsByIds(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMarketplaceMCPs(self, request, context):
        """Marketplace related RPCs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getMarketplaceMCPDetail(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def rateMCP(self, request, context):
        """Rating and usage tracking RPCs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def useMCP(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_MCPServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'createMCP': grpc.unary_unary_rpc_method_handler(
                    servicer.createMCP,
                    request_deserializer=mcp__pb2.CreateMCPRequest.FromString,
                    response_serializer=mcp__pb2.MCPResponse.SerializeToString,
            ),
            'getMCP': grpc.unary_unary_rpc_method_handler(
                    servicer.getMCP,
                    request_deserializer=mcp__pb2.GetMCPRequest.FromString,
                    response_serializer=mcp__pb2.MCPResponse.SerializeToString,
            ),
            'updateMCP': grpc.unary_unary_rpc_method_handler(
                    servicer.updateMCP,
                    request_deserializer=mcp__pb2.UpdateMCPRequest.FromString,
                    response_serializer=mcp__pb2.MCPResponse.SerializeToString,
            ),
            'deleteMCP': grpc.unary_unary_rpc_method_handler(
                    servicer.deleteMCP,
                    request_deserializer=mcp__pb2.DeleteMCPRequest.FromString,
                    response_serializer=mcp__pb2.DeleteMCPResponse.SerializeToString,
            ),
            'listMCPs': grpc.unary_unary_rpc_method_handler(
                    servicer.listMCPs,
                    request_deserializer=mcp__pb2.ListMCPsRequest.FromString,
                    response_serializer=mcp__pb2.ListMCPsResponse.SerializeToString,
            ),
            'getMCPsByIds': grpc.unary_unary_rpc_method_handler(
                    servicer.getMCPsByIds,
                    request_deserializer=mcp__pb2.GetMCPsByIdsRequest.FromString,
                    response_serializer=mcp__pb2.ListMCPsResponse.SerializeToString,
            ),
            'getMarketplaceMCPs': grpc.unary_unary_rpc_method_handler(
                    servicer.getMarketplaceMCPs,
                    request_deserializer=mcp__pb2.GetMarketplaceMCPsRequest.FromString,
                    response_serializer=mcp__pb2.GetMarketplaceMCPsResponse.SerializeToString,
            ),
            'getMarketplaceMCPDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.getMarketplaceMCPDetail,
                    request_deserializer=mcp__pb2.GetMarketplaceMCPDetailRequest.FromString,
                    response_serializer=mcp__pb2.GetMarketplaceMCPDetailResponse.SerializeToString,
            ),
            'rateMCP': grpc.unary_unary_rpc_method_handler(
                    servicer.rateMCP,
                    request_deserializer=mcp__pb2.RateMCPRequest.FromString,
                    response_serializer=mcp__pb2.RateMCPResponse.SerializeToString,
            ),
            'useMCP': grpc.unary_unary_rpc_method_handler(
                    servicer.useMCP,
                    request_deserializer=mcp__pb2.UseMCPRequest.FromString,
                    response_serializer=mcp__pb2.UseMCPResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mcp.MCPService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mcp.MCPService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class MCPService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def createMCP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/createMCP',
            mcp__pb2.CreateMCPRequest.SerializeToString,
            mcp__pb2.MCPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMCP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/getMCP',
            mcp__pb2.GetMCPRequest.SerializeToString,
            mcp__pb2.MCPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def updateMCP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/updateMCP',
            mcp__pb2.UpdateMCPRequest.SerializeToString,
            mcp__pb2.MCPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def deleteMCP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/deleteMCP',
            mcp__pb2.DeleteMCPRequest.SerializeToString,
            mcp__pb2.DeleteMCPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listMCPs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/listMCPs',
            mcp__pb2.ListMCPsRequest.SerializeToString,
            mcp__pb2.ListMCPsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMCPsByIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/getMCPsByIds',
            mcp__pb2.GetMCPsByIdsRequest.SerializeToString,
            mcp__pb2.ListMCPsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMarketplaceMCPs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/getMarketplaceMCPs',
            mcp__pb2.GetMarketplaceMCPsRequest.SerializeToString,
            mcp__pb2.GetMarketplaceMCPsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getMarketplaceMCPDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/getMarketplaceMCPDetail',
            mcp__pb2.GetMarketplaceMCPDetailRequest.SerializeToString,
            mcp__pb2.GetMarketplaceMCPDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def rateMCP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/rateMCP',
            mcp__pb2.RateMCPRequest.SerializeToString,
            mcp__pb2.RateMCPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def useMCP(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mcp.MCPService/useMCP',
            mcp__pb2.UseMCPRequest.SerializeToString,
            mcp__pb2.UseMCPResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
