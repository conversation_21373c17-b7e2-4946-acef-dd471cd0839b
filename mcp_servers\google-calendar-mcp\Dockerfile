FROM node:18-alpine
WORKDIR /usr/src/app

COPY package-lock.json .
COPY package.json .
RUN npm ci --only=production --ignore-scripts && npm cache clean --force

COPY scripts ./scripts
# Make the build script executable
RUN chmod +x ./scripts/build.js

COPY src ./src
COPY tsconfig.json .

# Add execute permissions to the scripts
RUN chmod +x scripts/*.js

RUN npm run postinstall

# Set environment variables
ENV NODE_ENV=production

EXPOSE 3000/tcp
ENTRYPOINT [ "node", "build/http-server.js" ]
