from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from enum import Enum


class OrgOwnerInfo(BaseModel):
    userId: str
    email: str
    fullName: str
    role: str


class OrganizationBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    website_url: Optional[str] = Field(None, max_length=500, alias="websiteUrl")
    industry: Optional[str] = Field(None, max_length=100)


class OrganizationCreate(OrganizationBase):
    pass  # Owner ID is derived from the authenticated user token


class OrganizationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)


class OrganizationResponse(OrganizationBase):
    id: str
    created_by: str = Field(None, max_length=500, alias="createdBy")
    created_at: str = Field(None, max_length=500, alias="createdAt")
    updated_at: str = Field(None, max_length=500, alias="updatedAt")

    class Config:
        from_attributes = True  # Pydantic v2 equivalent of from_attributes
        populate_by_name = True  # Pydantic v2 equivalent of validate_by_name


class UserOrganizationsResponse(OrganizationResponse):
    is_primary: bool
    is_admin: bool


class DeleteOrganizationResponse(BaseModel):
    success: bool
    message: str


class OrganizationListResponse(BaseModel):
    success: bool
    message: str
    organizations: List[UserOrganizationsResponse]

    class Config:
        from_attributes = True
        populate_by_name = True


class OrganizationMemberUpdate(BaseModel):
    user_id: str = Field(..., alias="userIdToAddOrRemove")

    class Config:
        validate_by_name = True


class InviteStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    EXPIRED = "expired"


class InviteBase(BaseModel):
    email: EmailStr = Field(..., description="Email address of the invitee")
    organisation_id: str = Field(..., description="ID of the organization", alias="organisationId")
    department: Optional[str] = Field(None, description="Department ID or name")
    role: Optional[str] = Field(None, description="Role for the invited user")
    permissions: Optional[List[str]] = Field(None, description="Specific permissions for the user")


class InviteCreate(InviteBase):
    pass


class InviteResponse(InviteBase):
    id: str = Field(..., description="Unique ID of the invite")
    created_by: str = Field(
        ..., description="ID of the user who created the invite", alias="createdBy"
    )
    status: InviteStatus = Field(..., description="Current status of the invite")
    created_at: str = Field(..., description="When the invite was created", alias="createdAt")
    updated_at: str = Field(..., description="When the invite was last updated", alias="updatedAt")

    class Config:
        from_attributes = True
        populate_by_name = True


class InviteListResponse(BaseModel):
    success: bool
    message: str
    invites: List[InviteResponse]
    total_count: int = Field(..., alias="totalCount")
    page: int
    page_size: int = Field(..., alias="pageSize")

    class Config:
        populate_by_name = True


class AcceptInviteRequest(BaseModel):
    invite_link: str = Field(..., description="The encoded invite link to accept")
