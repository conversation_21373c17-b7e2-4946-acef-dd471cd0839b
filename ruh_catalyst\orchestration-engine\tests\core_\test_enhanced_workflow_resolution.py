"""
Test cases for the enhanced workflow parameter resolution system.

These tests demonstrate the fixes for:
1. Single Result Resolution Failure
2. Duplicate Field Name Conflicts
3. Enhanced explicit mapping system
"""

import pytest
from unittest.mock import Mock, patch
from app.core_.workflow_utils import WorkflowUtils


class TestEnhancedWorkflowResolution:
    """Test the enhanced workflow parameter resolution system."""

    @pytest.fixture
    def workflow_utils(self):
        """Create a WorkflowUtils instance for testing."""
        with patch("app.core_.workflow_utils.load_schema") as mock_load_schema:
            mock_load_schema.return_value = {
                "type": "object",
                "properties": {
                    "nodes": {"type": "array"},
                    "transitions": {"type": "array"},
                },
                "required": ["nodes", "transitions"],
            }
            with patch("app.core_.workflow_utils.WorkflowStateManager"):
                utils = WorkflowUtils(workflow_id="test_workflow")
                utils.state_manager = Mock()
                return utils

    def test_single_result_resolution_success(self, workflow_utils):
        """
        Test that single results wrapped in 'result' key are properly resolved.

        This addresses the core issue where MCP servers return {"result": "actual_value"}
        and the system needs to access the nested value.
        """
        # Arrange: Mock transition results with nested result structure
        workflow_utils.state_manager.get_transition_result = Mock(
            return_value={"result": "extracted_text_content"}
        )

        input_data_configs = [
            {
                "from_transition_id": "text_extraction_transition",
                "mapping": [{"from_field": "result", "to_field": "input_text"}],
            }
        ]

        current_tool_params = [
            {"field_name": "input_text", "field_value": "${input_text}"}
        ]

        # Act
        result = workflow_utils._create_enhanced_flattened_results(
            {"text_extraction_transition": {"result": "extracted_text_content"}},
            workflow_utils._extract_explicit_mappings(input_data_configs),
            "current_transition",
        )

        # Assert
        assert "input_text" in result
        assert result["input_text"] == "extracted_text_content"

    def test_nested_result_resolution_success(self, workflow_utils):
        """
        Test that deeply nested results like 'result.result' are properly resolved.
        """
        # Arrange: Mock transition results with deeply nested structure
        nested_result = {
            "result": {"result": "deeply_nested_value", "status": "success"}
        }

        input_data_configs = [
            {
                "from_transition_id": "nested_transition",
                "mapping": [
                    {"from_field": "result.result", "to_field": "extracted_value"}
                ],
            }
        ]

        # Act
        explicit_mappings = workflow_utils._extract_explicit_mappings(
            input_data_configs
        )
        result = workflow_utils._create_enhanced_flattened_results(
            {"nested_transition": nested_result},
            explicit_mappings,
            "current_transition",
        )

        # Assert
        assert "extracted_value" in result
        assert result["extracted_value"] == "deeply_nested_value"

    def test_duplicate_field_conflict_resolution(self, workflow_utils):
        """
        Test that duplicate field names from multiple transitions are properly resolved.
        """
        # Arrange: Multiple transitions with same field names
        all_previous_results = {
            "transition_1": {"result": "value_from_transition_1"},
            "transition_2": {"result": "value_from_transition_2"},
        }

        # No explicit mappings - should use conflict resolution
        explicit_mappings = {}

        # Act
        result = workflow_utils._create_enhanced_flattened_results(
            all_previous_results, explicit_mappings, "current_transition"
        )

        # Assert: Should have namespaced fields to resolve conflicts
        assert "result" in result  # One of the values
        assert "result_transition_1" in result or "result_transition_2" in result
        assert "transition_1.result" in result or "transition_2.result" in result

    def test_multiple_source_mapping(self, workflow_utils):
        """
        Test that multiple sources can map to different target fields without conflicts.
        """
        # Arrange: Multiple transitions with different mappings
        input_data_configs = [
            {
                "from_transition_id": "text_source",
                "mapping": [{"from_field": "result", "to_field": "input_text"}],
            },
            {
                "from_transition_id": "metadata_source",
                "mapping": [
                    {"from_field": "result.sentiment", "to_field": "sentiment_data"}
                ],
            },
        ]

        all_previous_results = {
            "text_source": {"result": "extracted_text"},
            "metadata_source": {
                "result": {"sentiment": "positive", "confidence": 0.95}
            },
        }

        # Act
        explicit_mappings = workflow_utils._extract_explicit_mappings(
            input_data_configs
        )
        result = workflow_utils._create_enhanced_flattened_results(
            all_previous_results, explicit_mappings
        )

        # Assert: Should have both mapped values
        assert result["input_text"] == "extracted_text"
        assert result["sentiment_data"] == "positive"

    def test_missing_field_handling(self, workflow_utils):
        """
        Test that missing fields are handled gracefully.
        """
        # Arrange: Mapping to a non-existent field
        input_data_configs = [
            {
                "from_transition_id": "source_transition",
                "mapping": [
                    {"from_field": "non_existent_field", "to_field": "target_field"}
                ],
            }
        ]

        all_previous_results = {
            "source_transition": {
                "other_field": "some_value"
            }  # Missing the target field
        }

        # Act
        explicit_mappings = workflow_utils._extract_explicit_mappings(
            input_data_configs
        )
        result = workflow_utils._create_enhanced_flattened_results(
            all_previous_results, explicit_mappings
        )

        # Assert: Should not have the target field since source field doesn't exist
        assert "target_field" not in result
        # But should have other fields from backward compatibility
        assert "other_field" in result

    def test_backward_compatibility_field_matching(self, workflow_utils):
        """
        Test that the system maintains backward compatibility with simple field name matching.
        """
        # Arrange: No explicit mappings, should fall back to field name matching
        all_previous_results = {
            "simple_transition": {
                "user_name": "John Doe",
                "user_email": "<EMAIL>",
            }
        }

        # Act: No explicit mappings
        result = workflow_utils._create_enhanced_flattened_results(
            all_previous_results, {}  # No explicit mappings
        )

        # Assert: Should have all fields available for backward compatibility
        assert result["user_name"] == "John Doe"
        assert result["user_email"] == "<EMAIL>"

    def test_enhanced_path_extraction(self, workflow_utils):
        """
        Test the enhanced path extraction for complex nested structures.
        """
        # Arrange: Complex nested data structure
        complex_data = {
            "result": {
                "data": {
                    "items": [
                        {"id": 1, "value": "first_item"},
                        {"id": 2, "value": "second_item"},
                    ],
                    "metadata": {"total_count": 2, "status": "complete"},
                }
            }
        }

        # Act & Assert: Test various path extractions
        assert (
            workflow_utils._extract_value_by_enhanced_path(
                complex_data, "result.data.metadata.status"
            )
            == "complete"
        )
        assert (
            workflow_utils._extract_value_by_enhanced_path(
                complex_data, "result.data.metadata.total_count"
            )
            == 2
        )
        assert (
            workflow_utils._extract_value_by_enhanced_path(complex_data, "result")
            == complex_data["result"]
        )

    def test_explicit_mapping_extraction(self, workflow_utils):
        """
        Test that explicit mappings are correctly extracted from input data configs.
        """
        # Arrange: Input data configs with explicit mappings
        input_data_configs = [
            {
                "from_transition_id": "source_transition",
                "mapping": [{"from_field": "result.data", "to_field": "target_field"}],
            }
        ]

        # Act
        explicit_mappings = workflow_utils._extract_explicit_mappings(
            input_data_configs
        )

        # Assert: Should extract the mapping correctly
        assert "target_field" in explicit_mappings
        assert explicit_mappings["target_field"]["source_transition"] == "result.data"
