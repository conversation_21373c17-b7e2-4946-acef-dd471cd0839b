from datetime import datetime
from sqlalchemy import Column, String, DateTime, Enum, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import JSON
from sqlalchemy.orm import declarative_base

Base = declarative_base()

class AgentConfig(Base):
    __tablename__ = "agents-service"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    description = Column(String, nullable=False)
    config_url = Column(String, nullable=False)  # S3 URL where JSON schema is stored
    # owner_id = Column(String, ForeignKey("users.id"), nullable=False)  # NOTE: Reference existing users table (Cross Check)
    owner_id = Column(String, nullable=False)  
    visibility = Column(Enum('private', 'public', name='visibility_enum'), nullable=False)
    category = Column(Enum('marketing', name='category_enum'), nullable=True)  # New category column
    tags = Column(JSON, nullable=True)  # Array of tags for filtering/categorization
    status = Column(Enum('active', 'inactive', name='status_enum'), nullable=False, default='active')
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<AgentConfig {self.name}>"