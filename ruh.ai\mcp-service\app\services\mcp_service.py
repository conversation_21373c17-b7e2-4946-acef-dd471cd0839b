# app/services/mcp_config_service.py
from datetime import datetime, timezone
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, or_
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig
from app.models.mcp_rating import McpRating
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.utils.constants.constants import (
    McpStatus,
    McpVisibility,
    McpDepartment,
    UrlType,
)
from app.utils.kafka.kafka_service import KafkaProducer

from app.utils.MCP.fetch_tools import get_mcp_tools, tools_to_json_response

logger = structlog.get_logger(__name__)


class MCPConfigService(mcp_pb2_grpc.MCPServiceServicer):
    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createMCP(
        self, request: mcp_pb2.CreateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        db = self.get_db()
        logger.info(f"gRPC: create_mcp_request received for name: {request.name}")

        try:
            tools = get_mcp_tools(request.url)
            if not tools:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid MCP tools configuration")
                logger.error(f"gRPC: Invalid MCP tools configuration for name: {request.name}")
                return mcp_pb2.MCPResponse(success=False, message="Invalid MCP tools configuration")

            tools_json = tools_to_json_response(tools)
            if not tools_json:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid MCP tools configuration")
                logger.error(f"gRPC: Invalid MCP tools configuration for name: {request.name}")
                return mcp_pb2.MCPResponse(success=False, message="Invalid MCP tools configuration")

            new_mcp = McpConfig(
                logo=request.logo if request.logo else None,
                name=request.name,
                description=request.description,
                owner_id=request.owner.id,
                owner_type=mcp_pb2.OwnerType.Name(request.owner_type).lower(),
                user_ids=list(request.user_ids) if request.user_ids else [],
                url=request.url or None,
                url_type=mcp_pb2.UrlType.Name(request.url_type).lower(),
                git_url=request.git_url or None,
                mcp_tools_config=tools_json,
                department=mcp_pb2.MCPDepartment.Name(request.department).lower(),
                visibility=mcp_pb2.Visibility.Name(request.visibility).lower(),
                tags=json.loads(request.tags) if request.tags else {},
                status=mcp_pb2.Status.Name(request.status).lower(),
            )
            db.add(new_mcp)
            db.commit()
            db.refresh(new_mcp)

            # Send Kafka event (assuming kafka_producer is initialized in __init__)
            # self.kafka_producer.send_email_event_unified(
            #     email_type=SendEmailTypeEnum.MCP_CREATED.value,
            #     data={
            #         "emailId": request.owner.email,
            #         "userName": request.owner.full_name,
            #         "userId": request.owner.id,
            #         "fcmToken": request.owner.fcm_token,
            #         "mcpId": str(new_mcp.id),
            #         "mcpName": new_mcp.name,
            #         "title": "New MCP Created",
            #         "body": f"Your MCP '{new_mcp.name}' has been created successfully.",
            #         "link": f"{settings.FRONTEND_URL}/mcp/{str(new_mcp.id)}",
            #         "logo": f"{settings.FRONTEND_URL}/assets/logo.png"
            #     },
            #     action=["sendNotification", "sendMCPEmail"]
            # )

            logger.info(f"gRPC: mcp_created with id: {str(new_mcp.id)}")

            return mcp_pb2.MCPResponse(
                success=True,
                message=f"MCP '{request.name}' created successfully",
                mcp=self._mcp_to_protobuf(new_mcp),
            )

        except Exception as db_error:  # Catch specific DB errors if possible
            db.rollback()
            logger.error(
                f"gRPC: database_operation_failed for MCP '{request.name}': {str(db_error)}",
                exc_info=True,
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(
                f"Failed to store MCP: An internal error occurred."
            )  # Avoid exposing raw DB error
            return mcp_pb2.MCPResponse(
                success=False, message=f"Failed to store MCP due to an internal error."
            )
        finally:
            if db:
                db.close()

    def getMCP(
        self, request: mcp_pb2.GetMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        """
        Retrieves an MCP configuration by its unique ID.

        Args:
            request (mcp_pb2.GetMCPConfigRequest): The request containing the MCP configuration ID.
            context (grpc.ServicerContext): The gRPC context for error handling and metadata.

        Returns:
            mcp_pb2.MCPConfigResponse: The response containing the requested MCP configuration details.
        """
        db = self.get_db()
        logger.info("get_mcp_config_request", mcp_id=request.id)
        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.id).first()
            if mcp_config is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("MCP Config not found")
                logger.error("mcp_config_not_found", mcp_id=request.id)
                return mcp_pb2.MCPResponse(success=False, message="MCP Config not found")

            logger.info("mcp_config_retrieved", mcp_id=mcp_config.id)

            # Check if user_id is provided and if the user has already added this MCP
            is_added = False
            if request.HasField("user_id") and mcp_config.user_ids:
                is_added = request.user_id in mcp_config.user_ids
                logger.info(
                    "checking_if_user_added_mcp",
                    user_id=request.user_id,
                    mcp_id=mcp_config.id,
                    is_added=is_added,
                )

            # Get the protobuf representation and set is_added field
            mcp_proto = self._mcp_to_protobuf(mcp_config)
            mcp_proto.is_added = is_added

            return mcp_pb2.MCPResponse(
                success=True,
                message=f"MCP Config {mcp_config.name} retrieved successfully",
                mcp=mcp_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return mcp_pb2.MCPResponse(success=False, message=str(e))
        finally:
            db.close()

    def updateMCP(
        self, request: mcp_pb2.UpdateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        db = self.get_db()
        logger.info(
            f"gRPC updateMCP request for ID: {request.id}, paths: {request.update_mask.paths}"
        )

        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.id).first()
            if not mcp_config:
                logger.warning(f"MCP with ID {request.id} not found for update.")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.id} not found.")
                return mcp_pb2.MCPResponse(success=False, message="MCP not found.")

            if not request.update_mask.paths:
                logger.info(
                    f"UpdateMCP called for ID {request.id} with an empty field mask. No changes applied."
                )
                # Return current state or a specific message
                return mcp_pb2.MCPResponse(
                    success=True,  # Or False if empty mask is an error
                    message="No fields specified for update. No changes made.",
                    mcp=self._mcp_to_protobuf(mcp_config),
                )

            something_updated = False
            for field_path in request.update_mask.paths:
                something_updated = True
                if field_path == "name":
                    mcp_config.name = request.name
                if field_path == "logo":
                    mcp_config.logo = request.logo
                elif field_path == "description":
                    mcp_config.description = request.description
                elif field_path == "visibility":
                    mcp_config.visibility = mcp_pb2.Visibility.Name(request.visibility).lower()
                elif field_path == "user_ids":
                    mcp_config.user_ids = list(request.user_ids)
                elif field_path == "department":
                    mcp_config.department = mcp_pb2.MCPDepartment.Name(request.department).lower()
                elif field_path == "tags":
                    mcp_config.tags = json.loads(request.tags) if request.tags else {}
                elif field_path == "status":
                    mcp_config.status = mcp_pb2.Status.Name(request.status).lower()
                elif field_path == "url":
                    tools = get_mcp_tools(request.url)
                    if not tools:
                        context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                        context.set_details("Invalid MCP tools configuration")
                        logger.error(
                            f"gRPC: Invalid MCP tools configuration for name: {mcp_config.name}"
                        )
                        return mcp_pb2.MCPResponse(
                            success=False, message="Invalid MCP tools configuration"
                        )

                    tools_json = tools_to_json_response(tools)
                    if not tools_json:
                        context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                        context.set_details("Invalid MCP tools configuration")
                        logger.error(
                            f"gRPC: Invalid MCP tools configuration for name: {mcp_config.name}"
                        )
                        return mcp_pb2.MCPResponse(
                            success=False, message="Invalid MCP tools configuration"
                        )

                    mcp_config.url = request.url

                    mcp_config.mcp_tools_config = tools_json

                elif field_path == "url_type":
                    mcp_config.url_type = mcp_pb2.UrlType.Name(request.url_type).lower()
                elif field_path == "git_url":
                    mcp_config.git_url = request.git_url
                else:
                    logger.warning(f"Unknown field path in update_mask: {field_path}")

            if something_updated:
                mcp_config.updated_at = datetime.now(timezone.utc)
                db.commit()
                db.refresh(mcp_config)
                logger.info(
                    f"MCP {request.id} updated successfully. Fields: {request.update_mask.paths}"
                )

            else:
                logger.info(
                    f"MCP {request.id} - No recognized fields in update_mask. No database changes committed."
                )

            return mcp_pb2.MCPResponse(
                success=True,
                message=f"MCP '{mcp_config.name}' updated successfully.",
                mcp=self._mcp_to_protobuf(mcp_config),
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating MCP {request.id}: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error while updating MCP: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message="Internal server error.")
        finally:
            if db:
                db.close()

    def deleteMCP(
        self, request: mcp_pb2.DeleteMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.DeleteMCPResponse:
        """
        Deletes an MCP configuration from the database.

        Args:
            request (mcp_pb2.DeleteMCPConfigRequest): The request object containing the MCP ID to be deleted.
            context (grpc.ServicerContext): The gRPC context for handling request status and errors.

        Returns:
            mcp_pb2.DeleteMCPConfigResponse: A response object indicating the success or failure of the deletion.

        Raises:
            grpc.StatusCode.NOT_FOUND: If the specified MCP configuration does not exist.
            grpc.StatusCode.INTERNAL: If an internal error occurs during the deletion process.
        """
        db = self.get_db()
        try:
            logger.info("delete_mcp_config_request", mcp_id=request.id)
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.id).first()
            if mcp_config is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("MCP Config not found")
                return mcp_pb2.DeleteMCPResponse(success=False, message=f"MCP Config not found")

            db.delete(mcp_config)
            db.commit()
            logger.info("mcp_config_deleted", mcp_id=request.id)

            return mcp_pb2.DeleteMCPResponse(
                success=True, message=f"MCP Config {mcp_config.name} deleted successfully"
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return mcp_pb2.DeleteMCPResponse(success=False, message="Internal server error")
        finally:
            db.close()

    def listMCPs(
        self, request: mcp_pb2.ListMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        db = self.get_db()
        logger.info(
            f"gRPC listMCPs request: page={request.page}, page_size={request.page_size}, filters applied."
        )
        print(f"[DEBUG] request: {request}")
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10

        try:
            query = db.query(McpConfig)

            # Apply owner_id filter if provided (for specific user requests)
            if request.HasField("owner_id") and request.owner_id:
                query = query.filter(McpConfig.owner_id == request.owner_id)

            # Apply department filter
            if request.HasField("department") and request.department:
                query = query.filter(
                    McpConfig.department == McpDepartment[request.department.upper()]
                )

            # Apply visibility filter
            if request.HasField("visibility") and request.visibility:
                visibility_str = mcp_pb2.Visibility.Name(request.visibility).lower()
                query = query.filter(McpConfig.visibility == McpVisibility[visibility_str.upper()])

            # Apply status filter
            if request.HasField("status") and request.status:
                status_str = mcp_pb2.Status.Name(request.status).lower()
                query = query.filter(McpConfig.status == McpStatus[status_str.upper()])

            # Apply url_type filter
            if request.HasField("url_type") and request.url_type:
                url_type_str = mcp_pb2.UrlType.Name(request.url_type).lower()
                query = query.filter(McpConfig.url_type == UrlType[url_type_str.upper()])

            if request.tags:
                tags_dict = json.loads(request.tags)
                for key, value in tags_dict.items():
                    # This assumes PostgreSQL with JSONB support
                    query = query.filter(McpConfig.tags[key].astext == value)

            total = query.count()
            mcp_configs = (
                query.order_by(McpConfig.created_at.desc())
                .offset((page - 1) * page_size)
                .limit(page_size)
                .all()
            )

            total_pages = (total + page_size - 1) // page_size

            mcp_protos = [self._mcp_to_protobuf(mcp_config) for mcp_config in mcp_configs]

            logger.info(
                f"Retrieved {len(mcp_protos)} MCPs for page {page}. Total MCPs matching filters: {total}."
            )
            return mcp_pb2.ListMCPsResponse(
                mcps=mcp_protos,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            logger.error(f"Error listing MCPs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred while listing MCPs: {str(e)}")
            return mcp_pb2.ListMCPsResponse()
        finally:
            if db:
                db.close()

    def listMCPsMarketplace(
        self, request: mcp_pb2.GetMarketplaceMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        db = self.get_db()
        logger.info(
            f"gRPC listMCPs request: page={request.page}, page_size={request.page_size}, filters applied."
        )
        print(f"[DEBUG] request: {request}")
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10

        try:
            # Query MCPs with visibility set to PUBLIC
            query = db.query(McpConfig).filter(McpConfig.visibility == McpVisibility.PUBLIC)

            # Apply user_id filter if provided (for specific user requests)
            if request.HasField("user_id") and request.user_id:
                query = query.filter(McpConfig.owner_id == request.user_id)

            # Apply department filter
            if request.HasField("department") and request.department:
                query = query.filter(
                    McpConfig.department == McpDepartment[request.department.upper()]
                )

            if request.tags:
                tags_dict = json.loads(request.tags)
                for key, value in tags_dict.items():
                    # This assumes PostgreSQL with JSONB support
                    query = query.filter(McpConfig.tags[key].astext == value)
            total = query.count()
            mcp_configs = (
                query.order_by(McpConfig.created_at.desc())
                .offset((page - 1) * page_size)
                .limit(page_size)
                .all()
            )

            total_pages = (total + page_size - 1) // page_size

            mcp_protos = [self._mcp_to_protobuf(mcp_config) for mcp_config in mcp_configs]

            logger.info(
                f"Retrieved {len(mcp_protos)} MCPs for page {page}. Total MCPs matching filters: {total}."
            )
            return mcp_pb2.ListMCPsResponse(
                mcps=mcp_protos,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[ERROR] Error listing MCPs: {str(e)}")
            logger.error(f"Error listing MCPs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred while listing MCPs: {str(e)}")
            return mcp_pb2.ListMCPsResponse()
        finally:
            if db:
                db.close()

    def _mcp_to_protobuf(self, mcp: McpConfig) -> mcp_pb2.MCP:
        """
        Converts a McpConfig model instance to its protobuf representation.

        Args:
            mcp (McpConfig): The MCP configuration model instance

        Returns:
            mcp_pb2.MCP: The protobuf representation of the MCP
        """
        return mcp_pb2.MCP(
            id=str(mcp.id),
            logo=mcp.logo if mcp.logo else None,
            name=mcp.name,
            description=mcp.description,
            owner_id=mcp.owner_id,
            user_ids=mcp.user_ids if mcp.user_ids else None,
            owner_type=mcp.owner_type,
            url=mcp.url,
            url_type=mcp.url_type,
            git_url=mcp.git_url,
            visibility=mcp.visibility,
            tags=json.dumps(mcp.tags) if mcp.tags else "{}",
            status=mcp.status,
            department=mcp.department,
            mcp_tools_config=json.dumps(mcp.mcp_tools_config) if mcp.mcp_tools_config else None,
            created_at=mcp.created_at.isoformat(),
            updated_at=mcp.updated_at.isoformat(),
        )

    def getMCPsByIds(
        self, request: mcp_pb2.GetMCPsByIdsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        """
        Retrieves multiple MCP configurations by their IDs.

        Args:
            request (mcp_pb2.GetMCPsByIdsRequest): The request containing a list of MCP IDs
            context (grpc.ServicerContext): The gRPC service context

        Returns:
            mcp_pb2.ListMCPsResponse: Response containing the list of requested MCPs
        """
        db = self.get_db()
        logger.info("get_mcps_by_ids_request", ids_count=len(request.ids))

        try:
            # Query MCPs by the provided IDs
            mcps = db.query(McpConfig).filter(McpConfig.id.in_(request.ids)).all()

            # Convert to protobuf format
            mcp_list = [self._mcp_to_protobuf(mcp) for mcp in mcps]

            # Log the results
            logger.info("mcps_retrieved_by_ids", count=len(mcp_list))

            return mcp_pb2.ListMCPsResponse(
                mcps=mcp_list, total=len(mcp_list), page=1, total_pages=1
            )
        except Exception as e:
            logger.error("get_mcps_by_ids_error", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.ListMCPsResponse()
        finally:
            db.close()

    def _mcp_to_marketplace_mcp(self, mcp: McpConfig) -> mcp_pb2.MarketplaceMCP:
        """
        Converts a McpConfig model instance to its MarketplaceMCP protobuf representation.

        Args:
            mcp (McpConfig): The MCP configuration model instance

        Returns:
            mcp_pb2.MarketplaceMCP: The marketplace protobuf representation of the MCP
        """
        # Extract capabilities from mcp_tools_config if available
        capabilities = []
        if mcp.mcp_tools_config and isinstance(mcp.mcp_tools_config, dict):
            tools = mcp.mcp_tools_config.get("tools", [])
            if isinstance(tools, list):
                capabilities = [tool.get("name", "") for tool in tools if isinstance(tool, dict)]

        # Convert mcp_tools_config to JSON string if it's a dict
        mcp_tools_config_str = ""
        if mcp.mcp_tools_config:
            if isinstance(mcp.mcp_tools_config, dict):
                mcp_tools_config_str = json.dumps(mcp.mcp_tools_config)
            elif isinstance(mcp.mcp_tools_config, str):
                mcp_tools_config_str = mcp.mcp_tools_config

        return mcp_pb2.MarketplaceMCP(
            id=str(mcp.id),
            name=mcp.name,
            description=mcp.description,
            logo=mcp.logo if mcp.logo else "",
            mcp_tools_config=mcp_tools_config_str,
            visibility=mcp.visibility,
            owner_id=mcp.owner_id,
            owner_type=mcp.owner_type if mcp.owner_type else "",
            user_ids=mcp.user_ids if mcp.user_ids else [],
            department=mcp.department,
            tags=json.dumps(mcp.tags) if mcp.tags else "{}",
            status=mcp.status,
            url=mcp.url if mcp.url else "",
            url_type=mcp.url_type if mcp.url_type else "",
            git_url=mcp.git_url if mcp.git_url else "",
            created_at=mcp.created_at.isoformat(),
            updated_at=mcp.updated_at.isoformat(),
            average_rating=mcp.average_rating if mcp.average_rating else 0.0,
            use_count=mcp.use_count if mcp.use_count else 0,
            api_documentation="",  # Placeholder for API documentation
            capabilities=capabilities,
        )

    def getMarketplaceMCPs(
        self, request: mcp_pb2.GetMarketplaceMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPsResponse:
        """
        Retrieves a paginated list of public MCP configurations for the marketplace.

        Args:
            request: Contains pagination, search, and filter parameters
            context: gRPC service context

        Returns:
            Response containing a list of marketplace MCPs
        """
        db = self.get_db()
        try:
            logger.info(
                "get_marketplace_mcps_request", page=request.page, page_size=request.page_size
            )

            # Start with base query for public MCPs
            query = db.query(McpConfig).filter(
                McpConfig.visibility == McpVisibility.PUBLIC, McpConfig.status == McpStatus.ACTIVE
            )

            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    or_(McpConfig.name.ilike(search_term), McpConfig.description.ilike(search_term))
                )

            # Apply department filter if provided
            if request.HasField("department"):
                department_str = mcp_pb2.MCPDepartment.Name(request.department).lower()
                query = query.filter(McpConfig.department == department_str)

            # Apply tags filter if provided
            if request.tags:
                tags_dict = json.loads(request.tags)
                for key, value in tags_dict.items():
                    # This assumes PostgreSQL with JSONB support
                    query = query.filter(McpConfig.tags[key].astext == value)

            # Apply sorting
            if request.HasField("sort_by"):
                sort_by = request.sort_by
                if sort_by == "newest":
                    query = query.order_by(desc(McpConfig.created_at))
                elif sort_by == "oldest":
                    query = query.order_by(asc(McpConfig.created_at))
                elif sort_by == "most_popular":
                    # Placeholder for popularity-based sorting
                    # In a real implementation, you might have a downloads or usage count field
                    query = query.order_by(desc(McpConfig.created_at))
                elif sort_by == "highest_rated":
                    # Placeholder for rating-based sorting
                    # In a real implementation, you would join with a ratings table
                    query = query.order_by(desc(McpConfig.created_at))
            else:
                # Default sort by newest
                query = query.order_by(desc(McpConfig.created_at))

            # Get total count
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10

            mcps = query.offset((page - 1) * page_size).limit(page_size).all()

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1
            next_page = page + 1 if has_next else None
            prev_page = page - 1 if has_prev else None

            # Convert to marketplace MCP format
            marketplace_mcps = [self._mcp_to_marketplace_mcp(mcp) for mcp in mcps]

            return mcp_pb2.GetMarketplaceMCPsResponse(
                success=True,
                message="Marketplace MCPs retrieved successfully",
                mcps=marketplace_mcps,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=next_page,
                prev_page=prev_page,
            )

        except Exception as e:
            logger.error("get_marketplace_mcps_error", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.GetMarketplaceMCPsResponse(
                success=False, message="Failed to retrieve marketplace MCPs"
            )
        finally:
            db.close()

    def getMarketplaceMCPDetail(
        self, request: mcp_pb2.GetMarketplaceMCPDetailRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPDetailResponse:
        """
        Retrieves detailed information about a specific marketplace MCP.

        Args:
            request: Contains the ID of the marketplace MCP to retrieve and optional user_id
            context: gRPC service context

        Returns:
            Response containing detailed information about the marketplace MCP with is_added flag
        """
        db = self.get_db()
        try:
            logger.info("get_marketplace_mcp_detail_request", mcp_id=request.id)

            # Find the MCP
            mcp_config = (
                db.query(McpConfig)
                .filter(
                    McpConfig.id == request.id,
                    McpConfig.visibility == McpVisibility.PUBLIC,
                    McpConfig.status == McpStatus.ACTIVE,
                )
                .first()
            )

            if not mcp_config:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Marketplace MCP with ID {request.id} not found")
                return mcp_pb2.GetMarketplaceMCPDetailResponse(
                    success=False, message=f"Marketplace MCP with ID {request.id} not found"
                )

            # Convert to marketplace MCP format
            marketplace_mcp = self._mcp_to_marketplace_mcp(mcp_config)

            # Check if user_id is provided and if the user has already added this MCP
            is_added = False
            if request.HasField("user_id") and mcp_config.user_ids:
                is_added = request.user_id in mcp_config.user_ids
                logger.info(
                    "checking_if_user_added_marketplace_mcp",
                    user_id=request.user_id,
                    mcp_id=mcp_config.id,
                    is_added=is_added,
                )

            # Set the is_added field
            marketplace_mcp.is_added = is_added

            return mcp_pb2.GetMarketplaceMCPDetailResponse(
                success=True,
                message="Marketplace MCP details retrieved successfully",
                mcp=marketplace_mcp,
            )

        except Exception as e:
            logger.error("get_marketplace_mcp_detail_error", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.GetMarketplaceMCPDetailResponse(
                success=False, message="Failed to retrieve marketplace MCP details"
            )
        finally:
            db.close()

    def rateMCP(
        self, request: mcp_pb2.RateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.RateMCPResponse:
        """
        Rate an MCP and update its average rating.

        Args:
            request: Contains the MCP ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_mcp_request",
                mcp_id=request.mcp_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return mcp_pb2.RateMCPResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if MCP exists
            mcp = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found")
                return mcp_pb2.RateMCPResponse(
                    success=False,
                    message=f"MCP with ID {request.mcp_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this MCP
            existing_rating = (
                db.query(McpRating)
                .filter(McpRating.mcp_id == request.mcp_id, McpRating.user_id == request.user_id)
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.now(timezone.utc)
                db.commit()
                logger.info(
                    "updated_mcp_rating",
                    mcp_id=request.mcp_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = McpRating(
                    mcp_id=request.mcp_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_mcp_rating",
                    mcp_id=request.mcp_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = db.query(McpRating).filter(McpRating.mcp_id == request.mcp_id).all()
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update MCP with new average rating
            mcp.average_rating = average_rating
            mcp.updated_at = datetime.now(timezone.utc)
            db.commit()

            return mcp_pb2.RateMCPResponse(
                success=True,
                message=f"Rating for MCP {mcp.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.RateMCPResponse(
                success=False, message="Failed to update MCP rating", average_rating=0.0
            )
        finally:
            db.close()

    def useMCP(
        self, request: mcp_pb2.UseMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.UseMCPResponse:
        """
        Record usage of an MCP and increment its use count.

        Args:
            request: Contains the MCP ID and user ID
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated use count
        """
        db = self.get_db()
        try:
            logger.info("use_mcp_request", mcp_id=request.mcp_id, user_id=request.user_id)

            # Check if MCP exists
            mcp = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found")
                return mcp_pb2.UseMCPResponse(
                    success=False, message=f"MCP with ID {request.mcp_id} not found", use_count=0
                )

            # Check if user is authorized to use this MCP
            if mcp.visibility != McpVisibility.PUBLIC and request.user_id != mcp.owner_id:
                if not mcp.user_ids or request.user_id not in mcp.user_ids:
                    context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                    context.set_details("User is not authorized to use this MCP")
                    return mcp_pb2.UseMCPResponse(
                        success=False,
                        message="User is not authorized to use this MCP",
                        use_count=mcp.use_count,
                    )

            # Increment use count
            mcp.use_count += 1
            mcp.updated_at = datetime.now(timezone.utc)
            db.commit()

            logger.info(
                "mcp_usage_recorded",
                mcp_id=request.mcp_id,
                user_id=request.user_id,
                new_use_count=mcp.use_count,
            )

            return mcp_pb2.UseMCPResponse(
                success=True,
                message=f"Usage for MCP {mcp.name} recorded successfully",
                use_count=mcp.use_count,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error recording MCP usage: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.UseMCPResponse(
                success=False, message="Failed to record MCP usage", use_count=0
            )
        finally:
            db.close()
