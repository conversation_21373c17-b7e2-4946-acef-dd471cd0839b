#!/usr/bin/env python3
"""
Phase 1 Test Script for Agent Executor Service
Tests basic functionality without external dependencies
"""

import asyncio
import json
from unittest.mock import AsyncMock
from datetime import datetime

from app.models.execution import AgentExecutionRequest, ExecutionStatus
from app.core.agent_executor import AgentExecutor
from app.services.health_service import HealthService
from app.utils.correlation import CorrelationTracker


async def test_agent_executor():
    """Test basic agent executor functionality."""
    print("🧪 Testing Agent Executor...")
    
    # Create mock producer
    producer = AsyncMock()
    
    # Create executor
    executor = AgentExecutor(producer=producer)
    
    # Create test request
    request = AgentExecutionRequest(
        execution_id="test-exec-001",
        agent_id="test-agent-gpt4",
        user_id="test-user-123",
        task="Analyze the quarterly sales data and provide insights",
        parameters={"format": "json", "include_charts": True},
        priority=8,
        correlation_id="test-corr-abc123",
        source_service="api-gateway"
    )
    
    print(f"  📝 Request: {request.agent_id} - {request.task[:50]}...")
    
    # Execute
    result = await executor.execute_agent(request)
    
    # Verify results
    assert result.execution_id == request.execution_id
    assert result.correlation_id == request.correlation_id
    assert result.status == ExecutionStatus.COMPLETED
    assert result.result is not None
    assert result.execution_time > 0
    assert len(result.messages) > 0
    
    print(f"  ✅ Status: {result.status}")
    print(f"  ⏱️  Execution time: {result.execution_time:.2f}s")
    print(f"  📊 Messages: {len(result.messages)}")
    print(f"  💾 Resource usage: CPU {result.resource_usage['cpu_percent']}%, Memory {result.resource_usage['memory_mb']}MB")
    print("  ✅ Agent Executor test passed!\n")


async def test_health_service():
    """Test health service functionality."""
    print("🏥 Testing Health Service...")
    
    health_service = HealthService()
    
    # Test health status
    health_status = await health_service.get_health_status()
    assert "status" in health_status
    assert "timestamp" in health_status
    assert "uptime_seconds" in health_status
    assert health_status["service"] == "agent-executor-service"
    
    print(f"  ✅ Health Status: {health_status['status']}")
    print(f"  ⏱️  Uptime: {health_status['uptime_seconds']:.2f}s")
    
    # Test readiness status
    readiness_status = await health_service.get_readiness_status()
    assert "ready" in readiness_status
    assert isinstance(readiness_status["ready"], bool)
    
    print(f"  ✅ Readiness: {readiness_status['ready']}")
    print("  ✅ Health Service test passed!\n")


def test_correlation_tracker():
    """Test correlation tracker functionality."""
    print("🔗 Testing Correlation Tracker...")
    
    tracker = CorrelationTracker(timeout_seconds=60)
    
    # Generate correlation ID
    corr_id = tracker.generate_correlation_id()
    assert len(corr_id) > 0
    print(f"  🆔 Generated ID: {corr_id[:8]}...")
    
    # Track request
    request_data = {"test": "data", "timestamp": datetime.utcnow().isoformat()}
    tracker.track_request(corr_id, request_data)
    
    # Retrieve request
    retrieved = tracker.get_request(corr_id)
    assert retrieved is not None
    assert retrieved["request_data"]["test"] == "data"
    
    # Get stats
    stats = tracker.get_stats()
    assert stats["total_tracked"] >= 1
    assert stats["active_requests"] >= 1
    
    print(f"  📊 Stats: {stats['active_requests']} active, {stats['total_tracked']} total")
    
    # Remove request
    removed = tracker.remove_request(corr_id)
    assert removed is True
    
    print("  ✅ Correlation Tracker test passed!\n")


def test_data_models():
    """Test data model validation."""
    print("📋 Testing Data Models...")
    
    # Test valid request
    request = AgentExecutionRequest(
        execution_id="test-123",
        agent_id="agent-456",
        user_id="user-789",
        task="Test task",
        correlation_id="corr-abc",
        source_service="test-service"
    )
    
    assert request.priority == 5  # default value
    assert request.timeout == 300  # default value
    assert isinstance(request.created_at, datetime)
    
    print(f"  ✅ Valid request: {request.execution_id}")
    
    # Test JSON serialization
    request_json = request.model_dump()
    assert "execution_id" in request_json
    assert "created_at" in request_json
    
    print(f"  ✅ JSON serialization: {len(request_json)} fields")
    
    # Test validation
    try:
        AgentExecutionRequest(
            execution_id="test-123",
            agent_id="agent-456",
            user_id="user-789",
            task="Test task",
            priority=15,  # Invalid: > 10
            correlation_id="corr-abc",
            source_service="test-service"
        )
        assert False, "Should have raised validation error"
    except ValueError:
        print("  ✅ Validation error caught for invalid priority")
    
    print("  ✅ Data Models test passed!\n")


async def main():
    """Run all Phase 1 tests."""
    print("🚀 Agent Executor Service - Phase 1 Tests")
    print("=" * 50)
    
    try:
        # Test core components
        await test_agent_executor()
        await test_health_service()
        test_correlation_tracker()
        test_data_models()
        
        print("🎉 All Phase 1 tests passed!")
        print("\n📋 Phase 1 Implementation Summary:")
        print("  ✅ Project structure following mcp-executor-service patterns")
        print("  ✅ Core configuration management")
        print("  ✅ Basic Kafka service implementation")
        print("  ✅ Essential data models for agent execution")
        print("  ✅ Basic health check endpoints")
        print("  ✅ Core agent executor with placeholder logic")
        print("  ✅ Correlation tracking utilities")
        print("  ✅ Comprehensive error handling")
        
        print("\n🚧 Next Steps (Phase 2):")
        print("  🔄 Agent platform integration via Kafka")
        print("  🛠️  Tool integration migration (MCP, Workflow, Dynamic)")
        print("  💾 Session management with Redis")
        print("  🔧 Configuration retrieval from agent platform")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
