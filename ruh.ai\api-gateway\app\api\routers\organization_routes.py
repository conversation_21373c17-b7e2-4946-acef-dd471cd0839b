from fastapi import APIRouter, Depends, HTTPException, status, Query

from app.services.organisation_service import OrganisationServiceClient
from app.schemas.organization import (
    OrganizationCreate,
    OrganizationResponse,
    OrganizationListResponse,
    UserOrganizationsResponse,
    InviteCreate,
    InviteResponse,
    AcceptInviteRequest
)

from app.core.auth_guard import role_required
from app.utils.parse_error import parse_error


organization_router = APIRouter(prefix="/organizations", tags=["organizations"])
org_service = OrganisationServiceClient()


@organization_router.post(
    "/create",
    response_model=OrganizationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new organization",
    description="""
    This endpoint allows authenticated users to create a new organization.
    
    - The authenticated user automatically becomes the organization owner.
    - User must have either 'user' or 'admin' role to create an organization.
    - Organization name is required and must be between 1-100 characters.
    """,
    responses={
        201: {
            "description": "Organization created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "org_1234567890",
                        "name": "Acme Corporation",
                        "websiteUrl": "https://acme.com",
                        "industry": "Technology",
                        "createdBy": "user_1234567890",
                        "createdAt": "2025-05-14T10:30:00Z",
                        "updatedAt": "2025-05-14T10:30:00Z"
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid organization name"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User lacks required role"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def create_organization(
    org_data: OrganizationCreate,
    current_user: dict = Depends(role_required(["user", "admin"])) # Allow users/admins to create
):
    try:
        requester_id = current_user["user_id"]
        # Call the organization service with updated parameter names
        response = await org_service.create_organization(
            name=org_data.name,
            website_url=org_data.website_url,
            industry=org_data.industry,
            created_by=requester_id
        )

        org_dict = {
            "id": response.organisation.id,
            "name": response.organisation.name,
            "website_url": response.organisation.website_url,
            "industry": response.organisation.industry,
            "created_by": response.organisation.created_by,
            "created_at": response.organisation.created_at,
            "updated_at": response.organisation.updated_at,
            # Additional fields can be added here as needed
        }

        # Use model_validate for Pydantic v2
        return OrganizationResponse.model_validate(org_dict)

    except HTTPException as http_exc:
        raise http_exc # Re-raise HTTP exceptions from _handle_error
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@organization_router.post(
    "/invite",
    response_model=InviteResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Invite a user to an organization",
    description="""
    This endpoint allows organization owners/admins to invite users to join an organization.
    
    - An email invitation will be sent to the specified email address.
    - Optional role and department can be specified for the invited user.
    - Optional permissions list can be provided for fine-grained access control.
    """,
    responses={
        201: {
            "description": "Invite created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "inv_1234567890",
                        "email": "<EMAIL>",
                        "organisationId": "org_1234567890",
                        "department": "Engineering",
                        "role": "Developer",
                        "permissions": ["read:code", "write:code"],
                        "createdBy": "user_1234567890",
                        "status": "PENDING",
                        "createdAt": "2025-05-14T10:30:00Z",
                        "updatedAt": "2025-05-14T10:30:00Z"
                    }
                }
            },
        },
        400: {
            "description": "Invalid input",
            "content": {"application/json": {"example": {"detail": "Invalid email address"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "User lacks required permissions"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def invite_user(
    invite_data: InviteCreate,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Create an invitation for a user to join an organization.
    
    Parameters:
    - organization_id: ID of the organization to invite the user to
    - invite_data: Invitation details including email and optional role/department
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - InviteResponse: Created invitation details
    
    Raises:
    - HTTPException(400): If invitation creation fails
    - HTTPException(401): If user is not authenticated
    - HTTPException(403): If user lacks required permissions
    """
    try:
        # Override the organization ID from path parameter
        invite_data_dict = invite_data.model_dump()
        
        requester_id = current_user["user_id"]
        
        # Call the organization service
        response = await org_service.invite_user(
            email=invite_data.email,
            organisation_id=invite_data_dict["organisation_id"],
            created_by=requester_id,
            role=invite_data.role,
            department=invite_data.department,
            permissions=invite_data.permissions
        )

        # Map response to schema
        invite_dict = {
            "id": response.invite.id,
            "email": response.invite.email,
            "organisationId": response.invite.organisation_id,
            "department": response.invite.department,
            "role": response.invite.role,
            "permissions": list(response.invite.permissions) if response.invite.permissions else [],
            "createdBy": response.invite.created_by,
            "status": response.invite.status,
            "createdAt": response.invite.created_at,
            "updatedAt": response.invite.updated_at
        }

        return InviteResponse.model_validate(invite_dict)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

        
@organization_router.post(
    "/accept-invite",
    response_model=InviteResponse,
    summary="Accept organization invitation via link",
    description="""
    This endpoint allows a user to accept an organization invitation using an invite link.
    
    - The user must be authenticated.
    - The invite link is decoded to extract the invitation details.
    - The authenticated user's email is automatically used for verification.
    - If successful, the user is added to the organization in the specified role.
    """,
    responses={
        200: {
            "description": "Invite accepted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "inv_1234567890",
                        "email": "<EMAIL>",
                        "organisationId": "org_1234567890",
                        "department": "Engineering",
                        "role": "Developer",
                        "permissions": ["read:code", "write:code"],
                        "createdBy": "user_1234567890",
                        "status": "ACCEPTED",
                        "createdAt": "2025-05-14T10:30:00Z",
                        "updatedAt": "2025-05-14T10:30:00Z",
                        "acceptedAt": "2025-05-15T10:30:00Z",
                        "acceptedBy": "user_9876543210"
                    }
                }
            },
        },
        400: {
            "description": "Invalid invite link",
            "content": {"application/json": {"example": {"detail": "Invalid invite link"}}},
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Permission denied",
            "content": {"application/json": {"example": {"detail": "Email doesn't match invited user"}}},
        },
        404: {
            "description": "Invite not found",
            "content": {"application/json": {"example": {"detail": "Invite not found or expired"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def accept_invite(
    invite_data: AcceptInviteRequest,
    current_user: dict = Depends(role_required(["user"]))
):
    """
    Accept an organization invitation using an invite link.
    
    Parameters:
    - invite_data: The invite link details
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - InviteResponse: Accepted invitation details
    
    Raises:
    - HTTPException(400): If the invite link is invalid
    - HTTPException(401): If the user is not authenticated
    - HTTPException(403): If the user's email doesn't match the invited user
    - HTTPException(404): If the invite is not found, expired, or already processed
    - HTTPException(500): For internal server errors
    """
    try:
        # Extract user details from auth token
        user_id = current_user["user_id"]
        user_email = current_user["email"]
        user_name = current_user["full_name"]
        
        # Call the organization service with the invite link and auth user details
        response = await org_service.accept_invite_by_link(
            invite_link=invite_data.invite_link,
            auth_user_id=user_id,
            user_name=user_name,
            auth_user_email=user_email
        )
        
        # Map response to schema
        invite_dict = {
            "id": response.invite.id,
            "email": response.invite.email,
            "organisationId": response.invite.organisation_id,
            "department": response.invite.department,
            "role": response.invite.role,
            "permissions": list(response.invite.permissions) if response.invite.permissions else [],
            "createdBy": response.invite.created_by,
            "status": response.invite.status,
            "createdAt": response.invite.created_at,
            "updatedAt": response.invite.updated_at,
            # Include accepted details if available
            "acceptedAt": response.invite.accepted_at if hasattr(response.invite, "accepted_at") else None,
            "acceptedBy": response.invite.accepted_by if hasattr(response.invite, "accepted_by") else None
        }
        
        return InviteResponse.model_validate(invite_dict)
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

@organization_router.get(
    "/getUserOrganisations",
    response_model=OrganizationListResponse,
    status_code=status.HTTP_200_OK,
    summary="List organizations for current user",
    description="""
    This endpoint retrieves a list of all organizations that the authenticated user is a member of.
    
    - User must be authenticated.
    - Results can be paginated and filtered with search terms.
    - Returns basic information about each organization.
    """,
    responses={
        200: {
            "description": "List of organizations retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Organizations retrieved successfully",
                        "organizations": [
                            {
                                "id": "org_1234567890",
                                "name": "Acme Corporation",
                                "websiteUrl": "https://acme.com",
                                "industry": "Technology",
                                "createdBy": "user_1234567890",
                                "createdAt": "2025-05-14T10:30:00Z",
                                "updatedAt": "2025-05-14T10:30:00Z"
                            }
                        ]
                    }
                }
            },
        },
        401: {
            "description": "Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def list_user_organizations(
    page: int = Query(1, description="Page number for pagination", ge=1),
    page_size: int = Query(10, description="Number of items per page", ge=1, le=100),
    search: str = Query("", description="Search term to filter organizations by name"),
    current_user: dict = Depends(role_required(["user"]))
):
    """
    List all organizations for the current authenticated user.
    
    Parameters:
    - page: Page number (default: 1)
    - page_size: Number of items per page (default: 10, max: 100)
    - search: Optional search term to filter organizations by name
    - current_user: Authenticated user information (injected by dependency)
    
    Returns:
    - OrganizationListResponse: List of organizations with pagination details
    
    Raises:
    - HTTPException(401): If user is not authenticated
    - HTTPException(500): For internal server errors
    """
    try:
        # Get user ID from auth token
        user_id = current_user["user_id"]
        
        # Call the organization service to get all user organizations with relationship info
        user_orgs_response = await org_service.get_user_organisations(user_id=user_id)

        # Filter organizations if search term is provided
        filtered_orgs = []
        if search:
            for user_org in user_orgs_response.organisations:
                if search.lower() in user_org.organisation.name.lower():
                    filtered_orgs.append(user_org)
        else:
            filtered_orgs = user_orgs_response.organisations
        
        # Apply pagination
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_orgs = filtered_orgs[start_idx:end_idx]
        
        # Map response to schema
        organizations = []
        for user_org in paginated_orgs:
            org = user_org.organisation
            org_dict = {
                "id": org.id,
                "name": org.name,
                "website_url": org.website_url,
                "industry": org.industry,
                "created_by": org.created_by,
                "created_at": org.created_at,
                "updated_at": org.updated_at,
                "is_primary": user_org.is_primary,
                "is_admin": user_org.is_admin
            }
            organizations.append(UserOrganizationsResponse.model_validate(org_dict))
        
        return OrganizationListResponse(
            success=True,
            message="Organizations retrieved successfully",
            organizations=organizations
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
