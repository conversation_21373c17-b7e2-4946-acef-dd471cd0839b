# Agent Executor Service Configuration

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_EXECUTION_REQUEST_TOPIC=agent-execution-requests
KAFKA_EXECUTION_RESPONSE_TOPIC=agent-execution-responses
KAF<PERSON>_CONFIG_REQUEST_TOPIC=agent-config-requests
KAFKA_CONFIG_RESPONSE_TOPIC=agent-config-responses
KAFKA_CONSUMER_GROUP_ID=agent_executor_service

# Execution Configuration
MAX_CONCURRENT_EXECUTIONS=50
DEFAULT_EXECUTION_TIMEOUT=300
DEFAULT_EXECUTION_RETRIES=3

# Redis Configuration
REDIS_URL=redis://localhost:6379
SESSION_TTL_SECONDS=3600
SESSION_COMPRESSION_ENABLED=true

# Agent Platform Integration
AGENT_PLATFORM_BASE_URL=http://localhost:8001
CONFIG_CACHE_TTL=300

# Authentication and Security
SERVER_AUTH_KEY=your-server-auth-key-here
AUTH_API_BASE_URL=http://localhost:8000

# Monitoring and Logging
LOG_LEVEL=INFO
PROMETHEUS_PORT=9090
HEALTH_CHECK_PORT=8080

# Resource Management
MEMORY_LIMIT_MB=2048
CPU_LIMIT_PERCENT=80
CLEANUP_INTERVAL_SECONDS=300

# AutoGen Configuration
AUTOGEN_MODEL_TIMEOUT=60
AUTOGEN_MAX_MESSAGES=50
