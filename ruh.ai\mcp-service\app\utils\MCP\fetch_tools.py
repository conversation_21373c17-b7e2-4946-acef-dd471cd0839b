import asyncio
from typing import Any
import logging

from app.utils.MCP.mcp_client import MCPClient

#
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def _get_tools_async(server_url: str) -> Any:
    """
    Internal async function to connect to the MCP server and retrieve available tools.
    
    Args:
        server_url: The URL of the MCP server
        
    Returns:
        List of available tools
    """
    try:
        async with MCPClient(server_url) as client:
            tools = await client.list_tools()
            return tools
    except Exception as e:
        logger.error(f"Error getting tools from MCP server: {e}")
        raise

def get_mcp_tools(server_url: str) -> Any:
    """
    Connect to the MCP server and return the list of available tools.
    
    Args:
        server_url: The URL of the MCP server (e.g., "http://localhost:8080/sse")
        
    Returns:
        List of available tools
        
    Raises:
        Exception: If connection or tool retrieval fails
    """
    try:
        return asyncio.run(_get_tools_async(server_url))
    except Exception as e:
        logger.error(f"Failed to get MCP tools: {e}")
        raise

def tools_to_json_response(tools_result: Any) -> dict:
    """
    Convert MCP tool result into structured JSON with meta and nextCursor fields.

    Args:
        tools_result: The raw result returned from `get_mcp_tools(server_url)`,
                      typically an object with `meta`, `nextCursor`, and `tools` attributes.

    Returns:
        A dictionary that matches the desired JSON structure.
    """
    # If `tools_result` is a class with attributes
    return {
        "meta": getattr(tools_result, "meta", None),
        "nextCursor": getattr(tools_result, "nextCursor", None),
        "tools": [
            {
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema,  # Assuming already dict-like
                "annotations": tool.annotations,
            }
            for tool in getattr(tools_result, "tools", [])
        ]
    }

# Example usage
# if __name__ == "__main__":
#     try:
#         server_url = "http://localhost:8000/sse"
#         tools = get_mcp_tools(server_url)
#         print(f"Available tools: {tools}")
#     except Exception as e:
#         print(f"Error: {e}")