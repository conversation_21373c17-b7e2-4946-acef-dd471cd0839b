# health_service.py
import asyncio
import logging
import time
from typing import Dict, Any
from datetime import datetime

from app.config.config import settings


class HealthService:
    """
    Health check service for monitoring service status and dependencies.
    Provides health endpoints and system status information.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.start_time = time.time()
        self.logger.info("HealthService initialized.")

    async def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status of the service."""
        try:
            uptime = time.time() - self.start_time
            
            # Basic health checks
            kafka_status = await self._check_kafka_health()
            redis_status = await self._check_redis_health()
            memory_status = self._check_memory_usage()
            
            # Determine overall status
            overall_status = "healthy"
            if not kafka_status["healthy"] or not redis_status["healthy"]:
                overall_status = "unhealthy"
            elif memory_status["usage_percent"] > 90:
                overall_status = "degraded"
            
            return {
                "status": overall_status,
                "timestamp": datetime.utcnow().isoformat(),
                "uptime_seconds": uptime,
                "version": "1.0.0",
                "service": "agent-executor-service",
                "checks": {
                    "kafka": kafka_status,
                    "redis": redis_status,
                    "memory": memory_status
                }
            }
        except Exception as e:
            self.logger.error(f"Health check failed: {e}", exc_info=True)
            return {
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }

    async def get_readiness_status(self) -> Dict[str, Any]:
        """Get readiness status for Kubernetes readiness probe."""
        try:
            # Check if service is ready to accept requests
            kafka_ready = await self._check_kafka_connectivity()
            
            ready = kafka_ready
            
            return {
                "ready": ready,
                "timestamp": datetime.utcnow().isoformat(),
                "checks": {
                    "kafka_connectivity": kafka_ready
                }
            }
        except Exception as e:
            self.logger.error(f"Readiness check failed: {e}", exc_info=True)
            return {
                "ready": False,
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }

    async def _check_kafka_health(self) -> Dict[str, Any]:
        """Check Kafka connectivity and health."""
        try:
            # This is a placeholder - will be implemented with actual Kafka health check
            # For Phase 1, we'll return a basic status
            return {
                "healthy": True,
                "status": "connected",
                "broker": settings.kafka_bootstrap_servers,
                "last_check": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "healthy": False,
                "status": "error",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }

    async def _check_redis_health(self) -> Dict[str, Any]:
        """Check Redis connectivity and health."""
        try:
            # This is a placeholder - will be implemented with actual Redis health check
            # For Phase 1, we'll return a basic status
            return {
                "healthy": True,
                "status": "connected",
                "url": settings.redis_url,
                "last_check": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "healthy": False,
                "status": "error",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }

    def _check_memory_usage(self) -> Dict[str, Any]:
        """Check memory usage."""
        try:
            import psutil
            memory = psutil.virtual_memory()
            
            return {
                "healthy": memory.percent < 90,
                "usage_percent": memory.percent,
                "available_mb": memory.available / (1024 * 1024),
                "total_mb": memory.total / (1024 * 1024),
                "last_check": datetime.utcnow().isoformat()
            }
        except ImportError:
            # psutil not available, return basic info
            return {
                "healthy": True,
                "status": "monitoring_unavailable",
                "last_check": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }

    async def _check_kafka_connectivity(self) -> bool:
        """Check if Kafka is reachable."""
        try:
            # This is a placeholder - will be implemented with actual connectivity check
            # For Phase 1, we'll assume it's connected
            return True
        except Exception as e:
            self.logger.error(f"Kafka connectivity check failed: {e}")
            return False
