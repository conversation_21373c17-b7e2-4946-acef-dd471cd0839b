[tool.poetry]
name = "agent-executor"
version = "0.1.0"
description = "Centralized execution engine for all agents across the platform"
authors = ["RapidInnovation Team"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
# Kafka integration
aiokafka = "^0.12.0"
# Data validation and settings
pydantic = "^2.5.0"
pydantic-settings = "^2.8.1"
# AutoGen framework (will be added in Phase 3)
# autogen-agentchat = "^0.5.7"
# autogen-ext = {extras = ["openai"], version = "^0.5.7"}
# Redis for session management
redis = "^5.0.1"
# HTTP client for API calls
aiohttp = "^3.10.0"
# System monitoring
psutil = "^5.9.0"
# Async utilities
asyncio-mqtt = "^0.16.0"
# JSON handling
orjson = "^3.9.0"
# Logging and monitoring
structlog = "^23.2.0"
# Error handling
werkzeug = "^3.1.3"

[tool.poetry.group.dev.dependencies]
# Testing
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
# Code quality
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
# Development tools
pre-commit = "^3.4.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 100

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
