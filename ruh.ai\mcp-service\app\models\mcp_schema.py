import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Enum, Integer, Float
from sqlalchemy.dialects.postgresql import ARRAY

from sqlalchemy.dialects.postgresql import JSO<PERSON>
from sqlalchemy.orm import declarative_base
from app.utils.constants.constants import (
    McpVisibility,
    McpStatus,
    McpOwnerType,
    McpDepartment,
    UrlType,
)

Base = declarative_base()


class McpConfig(Base):
    __tablename__ = "test-mcp-configs"

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Basic information
    name = Column(String(255), nullable=False)
    description = Column(String, nullable=True)

    logo = Column(String, nullable=True)

    mcp_tools_config = Column(JSON, nullable=True)

    # Access control
    visibility = Column(Enum(McpVisibility), nullable=False, default=McpVisibility.PRIVATE)
    owner_id = Column(String, nullable=False)
    owner_type = Column(Enum(McpOwnerType), nullable=False)

    user_ids = Column(ARRAY(String), nullable=True)

    organization_user_ids = Column(ARRAY(String), nullable=True)

    use_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)

    # Classification
    department = Column(Enum(McpDepartment), nullable=False)
    tags = Column(JSON, nullable=True)  # Changed from ARRAY(String) to JSON
    status = Column(Enum(McpStatus), nullable=False, default=McpStatus.ACTIVE)

    url = Column(String, nullable=True)
    url_type = Column(Enum(UrlType), nullable=True)

    # Metadata
    git_url = Column(String, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<McpConfig(id={self.id}, name='{self.name}', category='{self.category.value}')>"
