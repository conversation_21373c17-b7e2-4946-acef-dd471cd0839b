# import json
# from fastapi import APIRouter, Depends, HTTPException, Query
# from app.services.user_service import UserService<PERSON>lient
# from app.services.workflow_service import WorkflowServiceClient
# from app.schemas.workflow import (
#     WorkflowCategoryEnum,
#     WorkflowTemplatePatchPayload,
#     WorkflowTemplateCreate,
#     WorkflowTemplateResponse,
#     CreateTemplateResponse,
#     UpdateTemplateResponse,
#     DeleteTemplateResponse,
#     ListTemplatesResponse,
#     CreateWorkflowFromTemplateResponse,
# )
# from google.protobuf.json_format import MessageToDict
# from app.core.auth_guard import role_required
# from typing import Optional
# from app.utils.parse_error import parse_error

# template_router = APIRouter(prefix="/workflow-templates", tags=["workflow-templates"])

# workflow_service = WorkflowServiceClient()
# user_service = UserServiceClient()


# @template_router.post("/create", response_model=CreateTemplateResponse)
# async def create_template(
#     template_data: WorkflowTemplateCreate,
#     current_user: dict = Depends(role_required(["user", "admin"])),
# ):
#     """
#     Create a new workflow template.

#     This endpoint allows users with 'user' or 'admin' roles to create new workflow templates
#     that can be reused to generate standard workflows.
#     """
#     try:
#         validate_response = await user_service.validate_user(current_user["user_id"])
#         if not validate_response["success"]:
#             raise HTTPException(status_code=400, detail=validate_response["message"])

#         user_details = validate_response["user"]

#         print(f"[DEBUG] Template data: {template_data}")
#         response = await workflow_service.create_template(
#             name=template_data.name,
#             description=template_data.description,
#             owner_details=user_details,
#             workflow_data=template_data.workflow_data,
#             category=template_data.category,
#             tags=template_data.tags,
#             status=template_data.status,
#             start_node_data=template_data.start_node_data,
#         )
#         print(f"[DEBUG] Template response: {response}")

#         return CreateTemplateResponse(success=response.success, message=response.message)
#     except HTTPException as e:
#         print(f"[ERROR] HTTP error in create_template: {str(e)}")
#         raise e
#     except Exception as e:
#         print(f"[ERROR] Unexpected error in create_template: {str(e)}")
#         error_details = parse_error(str(e))
#         print(f"[DEBUG] Error details: {error_details}")
#         raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# @template_router.patch("/{template_id}", response_model=UpdateTemplateResponse)
# async def update_workflow_template_endpoint(
#     template_id: str,
#     payload: WorkflowTemplatePatchPayload,
#     current_user: dict = Depends(role_required(["user", "admin"])),
# ):
#     """
#     Partially update an existing workflow template.
#     Only the fields provided in the request body will be updated.
#     """
#     try:
#         print(
#             f"[API ROUTE] PATCH request for template ID: {template_id} by user: {current_user.get('user_id')}"
#         )
#         print(f"[API ROUTE] Raw PATCH payload for template: {payload}")

#         validate_response = await user_service.validate_user(current_user["user_id"])
#         if not validate_response.get("success"):
#             raise HTTPException(
#                 status_code=400, detail=validate_response.get("message", "User validation failed")
#             )
#         user_details = validate_response["user"]

#         update_data = payload.model_dump(exclude_unset=True)
#         print(f"[API ROUTE] Filtered update_data for template PATCH: {update_data}")

#         if not update_data:
#             raise HTTPException(status_code=400, detail="No fields provided for template update.")

#         grpc_response = await workflow_service.patch_template(
#             template_id=template_id, update_fields=update_data, owner_details=user_details
#         )

#         if not grpc_response.success:
#             raise HTTPException(status_code=400, detail=grpc_response.message)

#         return UpdateTemplateResponse(success=grpc_response.success, message=grpc_response.message)

#     except HTTPException as http_exc:
#         raise http_exc
#     except ValueError as ve:
#         print(f"[API ROUTE EXCEPTION] ValueError for template: {ve}")
#         raise HTTPException(status_code=422, detail=str(ve))
#     except Exception as e:
#         print(f"[API ROUTE EXCEPTION] Unexpected error for template: {type(e).__name__} - {e}")
#         raise HTTPException(status_code=500, detail=f"An internal server error occurred: {str(e)}")


# @template_router.get("/{template_id}", response_model=WorkflowTemplateResponse)
# async def get_template(template_id: str):
#     """
#     Get a workflow template by ID.

#     This endpoint allows users with 'user' or 'admin' roles to retrieve a specific workflow template.
#     """
#     try:
#         print(f"[DEBUG] Getting template with ID: {template_id}")
#         response = await workflow_service.get_template(template_id)
#         print(f"[DEBUG] Template response: {response}")

#         if not response.success:
#             raise HTTPException(status_code=404, detail=response.message)

#         template_dict = MessageToDict(response.template, preserving_proto_field_name=True)
#         # template_dict = clean_workflow_dict(template_dict)

#         return WorkflowTemplateResponse(
#             success=response.success, message=response.message, template=template_dict
#         )
#     except Exception as e:
#         print(f"[ERROR] Error in get_template: {str(e)}")
#         error_details = parse_error(str(e))
#         raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# @template_router.delete("/{template_id}", response_model=DeleteTemplateResponse)
# async def delete_template(
#     template_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
# ):
#     """
#     Delete a workflow template.

#     This endpoint allows users with 'user' or 'admin' roles to delete a workflow template.
#     """
#     try:
#         print(f"[DEBUG] Deleting template with ID: {template_id}")
#         response = await workflow_service.delete_template(template_id)
#         print(f"[DEBUG] Template response: {response}")
#         return DeleteTemplateResponse(success=response.success, message=response.message)
#     except Exception as e:
#         error_details = parse_error(str(e))
#         print(f"[DEBUG] Error details: {error_details}")
#         raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# @template_router.get("", response_model=ListTemplatesResponse)
# async def list_templates(
#     page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
#     page_size: int = Query(10, ge=1, le=100, description="Number of templates per page (1-100)"),
#     category: Optional[str] = Query(
#         None,
#         description="Filter templates by category (e.g., 'WORKFLOW_CATEGORY_GENERAL', 'WORKFLOW_CATEGORY_SALES')",
#     ),
#     search: Optional[str] = Query(
#         None, description="Search term to filter templates by name or description"
#     ),
#     tags: Optional[str] = Query(
#         None,
#         description="Filter by tags using comma-separated key:value pairs (e.g., 'department:sales,priority:high')",
#     ),
# ):
#     """
#     List and filter workflow templates.

#     This endpoint retrieves a paginated list of workflow templates with optional filtering.

#     ## Filters
#     - **page**: Page number for pagination (starts at 1)
#     - **page_size**: Number of templates per page (1-100)
#     - **category**: Filter by workflow category (must be a valid category enum value)
#     - **search**: Search term to filter templates by name or description
#     - **tags**: Filter by tags using comma-separated key:value pairs

#     ## Tag Format
#     Tags should be provided as comma-separated key:value pairs, for example:
#     `department:sales,priority:high,version:1.0`

#     ## Response
#     Returns a paginated list of workflow templates matching the filter criteria.

#     ## Example
#     ```
#     GET /workflow-templates?page=1&page_size=10&category=WORKFLOW_CATEGORY_SALES&search=customer&tags=priority:high
#     ```
#     """
#     try:
#         print(
#             f"[DEBUG] Listing templates with filters: category={category}, search={search}, tags={tags}"
#         )

#         # Validate category if provided
#         if category and category not in [c.value for c in WorkflowCategoryEnum]:
#             raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

#         # Parse tags if provided (format: "key1:value1,key2:value2")
#         parsed_tags = None
#         if tags:
#             try:
#                 parsed_tags = {}
#                 for tag_pair in tags.split(","):
#                     if ":" in tag_pair:
#                         key, value = tag_pair.split(":", 1)
#                         parsed_tags[key.strip()] = value.strip()
#             except Exception as e:
#                 raise HTTPException(
#                     status_code=400,
#                     detail=f"Invalid tags format. Use 'key1:value1,key2:value2': {str(e)}",
#                 )

#         response = await workflow_service.list_templates(
#             page=page,
#             page_size=page_size,
#             category=category,
#             search=search,
#             tags=parsed_tags,
#         )

#         if not response.success:
#             raise HTTPException(status_code=400, detail=response.message)

#         templates = []
#         for template in response.templates:
#             template_dict = MessageToDict(template, preserving_proto_field_name=True)
#             templates.append(template_dict)

#         print(f"[DEBUG] Processed templates count: {len(templates)}")
#         return ListTemplatesResponse(
#             success=response.success,
#             message=response.message,
#             templates=templates,
#             total=response.total,
#             page=response.page,
#             total_pages=response.total_pages,
#         )
#     except Exception as e:
#         print(f"[ERROR] Error in list_templates: {str(e)}")
#         error_details = parse_error(str(e))
#         raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# @template_router.get("/user/{user_id}", response_model=ListTemplatesResponse)
# async def get_templates_by_user_id(
#     user_id: str,
#     page: int = Query(1, description="Page number", ge=1),
#     page_size: int = Query(10, description="Page size", ge=1, le=100),
#     category: Optional[str] = Query(None, description="Filter by workflow category"),
# ):
#     """
#     Retrieve a paginated list of workflows owned by a specific user.

#     This endpoint returns a list of workflows filtered by the provided user ID,
#     with pagination metadata included. Only users with the "user" role can access this endpoint.

#     :param user_id: The ID of the authenticated user making the request.
#     :type user_id: dict
#     :param page: The page number to retrieve (default is 1).
#     :type page: int
#     :param page_size: The number of workflows per page (default is 10, min is 1, max is 100).
#     :type page_size: int
#     :return: A paginated list of workflows with metadata.
#     :rtype: PaginatedWorkflowResponse
#     :raises HTTPException 500: If an internal server error occurs.
#     """
#     try:
#         # Validate enum values if provided
#         if category and category not in [c.value for c in WorkflowCategoryEnum]:
#             raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

#         response = await workflow_service.list_templates_by_user_id(
#             owner_id=user_id, page=page, page_size=page_size, category=category
#         )

#         if not response.success:
#             raise HTTPException(status_code=400, detail=response.message)

#         workflows = []
#         for workflow in response.templates:
#             workflow_dict = MessageToDict(workflow, preserving_proto_field_name=True)

#             # Parse `tags` if they are JSON strings
#             if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
#                 try:
#                     workflow_dict["tags"] = json.loads(workflow_dict["tags"])
#                 except json.JSONDecodeError:
#                     workflow_dict["tags"] = {}  # Assign empty dict instead of None

#             workflows.append(workflow_dict)

#         return ListTemplatesResponse(
#             success=response.success,
#             message=response.message,
#             templates=workflows,
#             total=response.total,
#             page=response.page,
#             total_pages=response.total_pages,
#         )

#     except Exception as e:
#         print(f"[EXCEPTION] {e}")
#         raise HTTPException(status_code=500, detail=str(e))


# @template_router.post(
#     "/templates/{template_id}/create-workflow", response_model=CreateWorkflowFromTemplateResponse
# )
# async def create_workflow_from_template(
#     template_id: str,
#     current_user: dict = Depends(role_required(["user"])),
# ):
#     """
#     Create a new workflow from a template.

#     This endpoint allows users to create a new workflow based on an existing template.
#     The new workflow will inherit properties from the template but will be owned by the current user.

#     Args:
#         template_id: The ID of the template to use
#         request_data: Request data containing owner type
#         current_user: The authenticated user making the request

#     Returns:
#         Response indicating success or failure of the workflow creation

#     Raises:
#         HTTPException: If user validation fails or any other error occurs
#     """
#     try:
#         # Validate user and get user details
#         validate_response = await user_service.validate_user(current_user["user_id"])
#         if not validate_response["success"]:
#             raise HTTPException(status_code=400, detail=validate_response["message"])

#         user_details = validate_response["user"]

#         print(f"[DEBUG] Owner type: {current_user['role']}")
#         # Convert owner_type to lowercase for consistency
#         owner_type = current_user["role"]
#         print(f"[DEBUG] Owner type: {owner_type}")

#         response = await workflow_service.create_workflow_from_template(
#             template_id=template_id, owner_type=owner_type, owner_details=user_details
#         )

#         return CreateWorkflowFromTemplateResponse(
#             success=response.success, message=response.message
#         )
#     except HTTPException as e:
#         print(f"[ERROR] HTTP error in create_workflow_from_template: {str(e)}")
#         raise e
#     except Exception as e:
#         error_details = parse_error(str(e))
#         print(f"[ERROR] Error in create_workflow_from_template: {str(e)}")
#         raise HTTPException(status_code=error_details["code"], detail=error_details["message"])
