# Workflow Routes Documentation

This document provides an overview of the workflow routes available in the API Gateway.

## Overview

The workflow routes provide endpoints for managing workflows in the system. These endpoints allow users to create, retrieve, update, and delete workflows, as well as perform various operations on them.

## Base URL

All workflow routes are prefixed with `/workflows`.

## Authentication

Most endpoints require authentication. The user must have either a `user` or `admin` role to access these endpoints.

## Endpoints

### Create Workflow

```
POST /workflows
```

Creates a new workflow.

**Request Body:**
- `name` (string, required): The name of the workflow
- `workflow_data` (object, required): The workflow definition data
- `start_node_data` (array, required): List of start node IDs

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result
- `workflow_id` (string): The ID of the created workflow

### Get Workflow

```
GET /workflows/{workflow_id}
```

Retrieves a workflow by its ID.

**Path Parameters:**
- `workflow_id` (string, required): The ID of the workflow to retrieve

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result
- `workflow` (object): The workflow details

### Get Workflow for Orchestration

```
GET /workflows/orchestration/{workflow_id}
```

Retrieves a workflow for orchestration purposes. This endpoint is intended for internal service-to-service communication.

**Path Parameters:**
- `workflow_id` (string, required): The ID of the workflow to retrieve

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result
- `workflow` (object): The workflow details

### Delete Workflow

```
DELETE /workflows/{workflow_id}
```

Deletes a workflow by its ID.

**Path Parameters:**
- `workflow_id` (string, required): The ID of the workflow to delete

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result

### List Workflows

```
GET /workflows
```

Retrieves a paginated list of all workflows.

**Query Parameters:**
- `page` (integer, default: 1): Page number for pagination
- `page_size` (integer, default: 10): Number of workflows per page (1-100)
- `category` (string, optional): Filter by workflow category
- `status` (string, optional): Filter by workflow status (active/inactive/draft)
- `visibility` (string, optional): Filter by workflow visibility (public/private)
- `search` (string, optional): Search term to filter workflows by name or description
- `tags` (string, optional): Filter by tags using comma-separated key:value pairs (e.g., 'department:sales,priority:high')

**Response:**
- `data` (array): List of workflows
- `metadata` (object): Pagination metadata

### Update Workflow Details

```
PATCH /workflows/update-details/{workflow_id}
```

Updates the details of an existing workflow.

**Path Parameters:**
- `workflow_id` (string, required): The ID of the workflow to update

**Request Body:**
- `name` (string, optional): New name for the workflow
- `description` (string, optional): New description
- `workflow_data` (object, optional): Updated workflow definition data
- `start_node_data` (array, optional): Updated start node data
- `user_ids` (array, optional): List of user IDs with access to the workflow
- `visibility` (string, optional): Visibility setting (public/private)
- `category` (string, optional): Workflow category
- `tags` (object, optional): Key-value pairs for tagging the workflow
- `status` (string, optional): Workflow status
- `version` (string, optional): Version string
- `is_changes_marketplace` (boolean, optional): Marketplace flag
- `toggle_visibility` (boolean, optional): If true, toggles the workflow visibility

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result

### Get User Workflows

```
GET /workflows/user/{user_id}
```

Retrieves a paginated list of workflows owned by a specific user.

**Path Parameters:**
- `user_id` (string, required): The ID of the user

**Query Parameters:**
- `page` (integer, default: 1): Page number for pagination
- `page_size` (integer, default: 10): Number of workflows per page (1-100)
- `category` (string, optional): Filter by workflow category
- `status` (string, optional): Filter by workflow status (active/inactive)
- `visibility` (string, optional): Filter by workflow visibility (public/private)

**Response:**
- `data` (array): List of workflows
- `metadata` (object): Pagination metadata

### Get Workflows by IDs

```
POST /workflows/by-ids
```

Retrieves multiple workflows by their IDs.

**Request Body:**
- `ids` (array, required): List of workflow IDs to retrieve

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result
- `workflows` (array): List of workflows
- `total` (integer): Total number of workflows retrieved

### Toggle Workflow Visibility

```
POST /workflows/{workflow_id}/toggle-visibility
```

Toggles the visibility of a workflow between PUBLIC and PRIVATE.

**Path Parameters:**
- `workflow_id` (string, required): The ID of the workflow to toggle

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result
- `workflow` (object): The updated workflow

### Update Workflow (Legacy)

```
PATCH /workflows/{workflow_id}
```

Updates an existing workflow. This endpoint is deprecated; use `/update-details/{workflow_id}` instead.

### Update Workflow Settings

```
PATCH /workflows/{workflow_id}/settings
```

Updates specific settings of a workflow.

**Path Parameters:**
- `workflow_id` (string, required): The ID of the workflow to update

**Request Body:**
- `status` (string, optional): New workflow status
- `is_changes_marketplace` (boolean, optional): New marketplace flag

**Response:**
- `success` (boolean): Indicates if the operation was successful
- `message` (string): A message describing the result
