# Output Schema Support for Tools

<!-- Provide a brief summary of your changes -->
This PR adds automatic JSON Schema generation for tool return types, enabling LLMs to understand the structure of data they'll receive from tools.

## Motivation and Context
<!-- Why is this change needed? What problem does it solve? -->
- Previously, tools only had input schemas but no structured metadata about their outputs
- This helps LLMs better understand the structure of data they'll receive from tools
- Particularly valuable for complex return types like Pydantic models and nested structures
- Improves the developer experience by making tools more self-documenting

## How Has This Been Tested?
<!-- Have you tested this in a real application? Which scenarios were tested? -->
- Added comprehensive test suite in `test_tool_manager.py` with the `TestOutputSchema` class
- Tests cover primitive types, Pydantic models, complex nested structures, and generic types
- Verified integration with FastMCP tool listing
- Tested with the output_schema_demo.py example which demonstrates various return types

## Breaking Changes
<!-- Will users need to update their code or configurations? -->
- Non-breaking change - adds functionality without modifying existing behavior
- Existing tools will automatically gain output schemas based on their return type annotations
- No changes required to existing code

## Types of changes
<!-- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [x] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)
- [x] Documentation update

## Checklist
<!-- Go over all the following points, and put an `x` in all the boxes that apply. -->
- [x] I have read the [MCP Documentation](https://modelcontextprotocol.io)
- [x] My code follows the repository's style guidelines
- [x] New and existing tests pass locally
- [x] I have added appropriate error handling
- [x] I have added or updated documentation as needed

## Additional context
<!-- Add any other context, implementation notes, or design decisions -->
- The implementation uses Python's type annotations and inspect module to extract return type information
- For primitive types (str, int, float, bool, dict, list), it maps directly to JSON Schema types
- For Pydantic models, it leverages Pydantic's built-in JSON Schema generation
- The feature gracefully handles cases where schema generation isn't possible, falling back to None
- Updated README.md with comprehensive examples showing different return type scenarios
