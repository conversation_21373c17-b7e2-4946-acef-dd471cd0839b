# kafka_service.py
import asyncio
import json
import logging
from typing import Optional, Any

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore
from aiokafka.structs import TopicPartition  # type: ignore
from aiokafka.errors import KafkaError, IllegalStateError  # type: ignore
from app.config.config import settings
from app.models.execution import AgentExecutionRequest, AgentExecutionResponse, ExecutionStatus
from app.core.agent_executor import AgentExecutor


class InfiniteSemaphore(asyncio.Semaphore):
    """A semaphore that never times out when acquiring."""

    async def acquire(self):
        """Acquire the semaphore with no timeout."""
        # Call the parent's acquire method directly without timeout
        # This will wait indefinitely until a slot is available
        return await super().acquire()


class KafkaAgentService:
    """
    Kafka service for agent execution following mcp-executor-service pattern.
    Handles message processing, execution coordination, and response management.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.kafka_broker: str = settings.kafka_bootstrap_servers
        self.execution_request_topic: str = settings.kafka_execution_request_topic
        self.execution_response_topic: str = settings.kafka_execution_response_topic
        self.group_id: str = settings.kafka_consumer_group_id

        self.consumer: AIOKafkaConsumer = AIOKafkaConsumer(
            self.execution_request_topic,
            bootstrap_servers=self.kafka_broker,
            group_id=self.group_id,
            auto_offset_reset="latest",
            enable_auto_commit=False,
        )
        self.producer: AIOKafkaProducer = AIOKafkaProducer(
            bootstrap_servers=self.kafka_broker,
            max_request_size=524288000,  # 500MB for large responses
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )

        self.agent_executor = AgentExecutor(producer=self.producer, logger=self.logger)

        self.max_concurrent_executions = settings.max_concurrent_executions
        # Use InfiniteSemaphore to prevent timeout issues
        self.semaphore = InfiniteSemaphore(self.max_concurrent_executions)

        self.logger.info(
            f"KafkaAgentService initialized. Request Topic: {self.execution_request_topic}, "
            f"Response Topic: {self.execution_response_topic}, Group ID: {self.group_id}"
        )
        self._consumer_task: Optional[asyncio.Task] = None

    async def start_consumer(self) -> None:
        """Starts the Kafka consumer and producer, and runs the message processing loop."""
        self.logger.info("Starting Kafka consumer and producer...")
        try:
            while True:
                try:
                    await self.consumer.start()
                    await self.producer.start()
                    self.logger.info(
                        "Kafka consumer and producer started successfully."
                    )
                except KafkaError as e:
                    self.logger.error(
                        f"Failed to start Kafka consumer/producer: {e}", exc_info=True
                    )
                    raise

                try:
                    async for msg in self.consumer:
                        self.logger.debug(
                            f"Received message: Topic={msg.topic}, Partition={msg.partition}, Offset={msg.offset}"
                        )
                        await self.semaphore.acquire()
                        asyncio.create_task(self.process_message(msg, self.semaphore))

                except IllegalStateError:
                    self.logger.warning(
                        "Kafka consumer is not running. Likely stopped."
                    )
                except asyncio.CancelledError:
                    self.logger.info("Consumer task cancelled.")
                except Exception as e:
                    self.logger.error(
                        f"Unexpected error in consumer loop: {e}", exc_info=True
                    )
        except asyncio.CancelledError:
            self.logger.info("Consumer task cancelled.")
        finally:
            self.logger.info("Consumer loop finished or terminated. Cleaning up...")
            await self.stop_consumer()

    async def stop_consumer(self) -> None:
        """Stops the Kafka consumer and producer."""
        self.logger.info("Attempting to stop Kafka consumer and producer...")
        if self.consumer._running:
            try:
                await self.consumer.stop()
                self.logger.info("Kafka consumer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping Kafka consumer: {e}", exc_info=True)
        else:
            self.logger.info("Kafka consumer already stopped or not started.")

        if self.producer._sender._running:
            try:
                await self.producer.stop()
                self.logger.info("Kafka producer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping Kafka producer: {e}", exc_info=True)
        else:
            self.logger.info("Kafka producer already stopped or not started.")

    async def send_error_response(
        self,
        reply_topic: str,
        error_message: str,
        execution_id: str,
        correlation_id: str,
    ) -> None:
        """Send error response to Kafka topic."""
        response = AgentExecutionResponse(
            execution_id=execution_id,
            correlation_id=correlation_id,
            status=ExecutionStatus.FAILED,
            error=error_message
        )
        
        if reply_topic:
            await self.producer.send_and_wait(reply_topic, value=response.dict())
            self.logger.info(f"Sent error response to topic '{reply_topic}': {response.dict()}")

    async def process_message(self, msg, semaphore: asyncio.Semaphore):
        """Processes a single Kafka message, executes the agent, and handles commits."""
        # Release the semaphore immediately after task creation
        # This allows the consumer to process more messages without waiting for this one to complete
        semaphore.release()
        self.logger.debug(
            f"Semaphore released early. Current queue size: {self.semaphore._value}"
        )

        topic_partition = TopicPartition(msg.topic, msg.partition)
        offset = msg.offset
        commit_offset = True

        payload = None
        try:
            payload = json.loads(msg.value.decode("utf-8"))
            self.logger.info(f"Processing execution request from offset {offset}: {payload}")

            # Parse execution request
            try:
                execution_request = AgentExecutionRequest.parse_obj(payload)
            except Exception as e:
                self.logger.error(
                    f"Invalid execution request payload at offset {offset}: {e}. Payload: {payload}"
                )
                commit_offset = True
                await self._commit_offset(topic_partition, offset, commit_offset)
                return

            # Extract headers for correlation tracking
            headers = self.decode_headers(msg.headers) if msg.headers else {}
            reply_topic = headers.get("reply-topic", self.execution_response_topic)

            # Execute agent
            try:
                result = await self.agent_executor.execute_agent(execution_request)
                
                self.logger.info(
                    f"Successfully processed execution request offset={offset} for agent {execution_request.agent_id}. "
                    f"Status: {result.status}"
                )

                # Send successful response
                await self.send_result(result, reply_topic)
                commit_offset = True
                
            except Exception as e:
                self.logger.error(
                    f"Agent execution failed for request at offset {offset}: {e}",
                    exc_info=True
                )
                await self.send_error_response(
                    reply_topic=reply_topic,
                    error_message=str(e),
                    execution_id=execution_request.execution_id,
                    correlation_id=execution_request.correlation_id,
                )

                # Send to dead letter queue for permanent failures
                dead_letter_topic = "agent_execution_dead_letter_queue"
                dlq_payload = {
                    "original_topic": msg.topic,
                    "partition": msg.partition,
                    "offset": offset,
                    "payload": payload,
                    "error": str(e),
                }
                try:
                    await self.producer.send_and_wait(
                        dead_letter_topic, value=dlq_payload
                    )
                    self.logger.info(
                        f"Sent message to DLQ '{dead_letter_topic}': {dlq_payload}"
                    )
                except Exception as dlq_err:
                    self.logger.error(
                        f"Failed to send message to DLQ '{dead_letter_topic}': {dlq_err}",
                        exc_info=True,
                    )
                commit_offset = True

        except json.JSONDecodeError:
            self.logger.error(
                f"Failed to decode JSON message at offset {offset}: {msg.value.decode('utf-8', errors='ignore')}"
            )
            commit_offset = True
        except Exception as e:
            self.logger.error(
                f"Unexpected error processing message at offset {offset}: {e}",
                exc_info=True,
            )
            commit_offset = False  # DO NOT COMMIT - let Kafka redeliver
        finally:
            # Commit the offset if needed
            await self._commit_offset(topic_partition, offset, commit_offset)

    def decode_headers(self, headers) -> dict:
        """Decode Kafka message headers."""
        decoded = {}
        if headers:
            for key, value in headers:
                try:
                    decoded[key] = value.decode('utf-8') if isinstance(value, bytes) else value
                except Exception as e:
                    self.logger.warning(f"Failed to decode header {key}: {e}")
        return decoded

    async def _commit_offset(self, topic_partition, offset, commit_offset):
        """Helper method to commit Kafka offset"""
        if commit_offset:
            try:
                self.logger.debug(
                    f"Committing offset {offset + 1} for partition {topic_partition}"
                )
                await self.consumer.commit({topic_partition: offset + 1})
            except Exception as e:
                self.logger.error(
                    f"Failed to commit offset {offset + 1} for partition {topic_partition}: {e}",
                    exc_info=True,
                )

    async def send_result(self, result: AgentExecutionResponse, reply_topic: str):
        """Send execution result to Kafka topic."""
        try:
            self.logger.info(f"Sending result to topic '{reply_topic}': {result.dict()}")
            await self.producer.send_and_wait(reply_topic, value=result.dict())
        except KafkaError as e:
            self.logger.error(
                f"Failed to send result to Kafka topic '{reply_topic}': {e}",
                exc_info=True,
            )
        except Exception as e:
            self.logger.error(f"Unexpected error sending result: {e}", exc_info=True)
