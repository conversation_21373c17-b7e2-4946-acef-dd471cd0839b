from sqlalchemy import Column, String, DateTime, Index
from sqlalchemy.sql import func
from app.db.database import Base
import uuid

class OAuthCredential(Base):
    __tablename__ = "oauth_credentials"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False)
    mcp_id = Column(String, nullable=False)
    tool_name = Column(String, nullable=False)
    
    # Composite key for lookup
    composite_key = Column(String, nullable=False, unique=True)
    
    # Secret Manager reference
    secret_reference = Column(String, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_used_at = Column(DateTime, default=func.now())

    # Create an index on the composite key for faster lookups
    __table_args__ = (
        Index('idx_oauth_composite_key', 'composite_key'),
    )

    def __repr__(self):
        return f"<OAuthCredential {self.composite_key}>"
