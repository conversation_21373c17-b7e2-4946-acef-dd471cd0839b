import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { createHTTPServer } from './http-server.js';
import express from 'express';

describe('MCP StreamableHTTP Server', () => {
  let app: express.Express;
  let server: any;

  beforeAll(async () => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.PORT = '3001';

    // Create the HTTP server
    app = await createHTTPServer();

    // Start server for testing
    server = app.listen(3001);
  });

  afterAll(async () => {
    if (server) {
      server.close();
    }
  });

  it('should respond to health check', async () => {
    const response = await fetch('http://localhost:3001/health');
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.status).toBe('healthy');
    expect(data.service).toBe('google-calendar-mcp');
  });

  it('should respond to root endpoint with MCP info', async () => {
    const response = await fetch('http://localhost:3001/');
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.message).toBe('Google Calendar MCP Server');
    expect(data.protocol).toBe('MCP StreamableHTTP');
    expect(data.capabilities.streaming).toBe(true);
    expect(data.endpoints.sse).toBeDefined();
    expect(data.endpoints.message).toBeDefined();
  });

  it('should reject SSE without proper Accept header', async () => {
    const response = await fetch('http://localhost:3001/sse');

    expect(response.status).toBe(406);
    const data = await response.json();
    expect(data.error).toContain('text/event-stream');
  });

  it('should handle JSON-RPC tools/list request', async () => {
    const response = await fetch('http://localhost:3001/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/list',
        id: 1
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.jsonrpc).toBe('2.0');
    expect(data.result.tools).toBeDefined();
    expect(Array.isArray(data.result.tools)).toBe(true);
    expect(data.id).toBe(1);
  });

  it('should reject invalid JSON-RPC format', async () => {
    const response = await fetch('http://localhost:3001/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        method: 'tools/list', // Missing jsonrpc field
        id: 1
      })
    });

    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.error.code).toBe(-32600);
    expect(data.error.message).toContain('JSON-RPC 2.0');
  });

  it('should handle unknown method', async () => {
    const response = await fetch('http://localhost:3001/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'unknown/method',
        id: 1
      })
    });

    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.error.code).toBe(-32601);
    expect(data.error.message).toContain('Method not found');
  });

  // Legacy endpoint tests
  it('should list tools via legacy endpoint', async () => {
    const response = await fetch('http://localhost:3001/tools');
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.tools).toBeDefined();
    expect(Array.isArray(data.tools)).toBe(true);
  });
});
