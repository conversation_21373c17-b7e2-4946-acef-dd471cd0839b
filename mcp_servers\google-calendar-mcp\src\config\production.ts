/**
 * Production configuration for Google Calendar MCP HTTP Server
 */

export interface ProductionConfig {
  port: number;
  nodeEnv: string;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  maxRequestSize: string;
  requestTimeout: number;
  maxConcurrentRequests: number;
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  corsOrigins: string[];
  enableMetrics: boolean;
  healthCheckPath: string;
  metricsPath: string;
}

export const getProductionConfig = (): ProductionConfig => {
  return {
    port: parseInt(process.env.PORT || '3000', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    logLevel: (process.env.LOG_LEVEL as any) || 'info',
    maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
    requestTimeout: parseInt(process.env.REQUEST_TIMEOUT || '30000', 10),
    maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '100', 10),
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10),
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['*'],
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    healthCheckPath: process.env.HEALTH_CHECK_PATH || '/health',
    metricsPath: process.env.METRICS_PATH || '/metrics',
  };
};

export const isProduction = (): boolean => {
  return process.env.NODE_ENV === 'production';
};

export const isDevelopment = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

export const isTest = (): boolean => {
  return process.env.NODE_ENV === 'test';
};
