# test_basic_functionality.py
import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from app.models.execution import AgentExecutionRequest, ExecutionStatus
from app.core.agent_executor import AgentExecutor
from app.services.health_service import HealthService


class TestAgentExecutor:
    """Test cases for AgentExecutor basic functionality."""

    @pytest.fixture
    def mock_producer(self):
        """Mock Kafka producer for testing."""
        producer = Mock()
        producer.send = AsyncMock()
        return producer

    @pytest.fixture
    def agent_executor(self, mock_producer):
        """Create AgentExecutor instance for testing."""
        return AgentExecutor(producer=mock_producer)

    @pytest.fixture
    def sample_execution_request(self):
        """Create sample execution request for testing."""
        return AgentExecutionRequest(
            execution_id="test-exec-123",
            agent_id="test-agent-456",
            user_id="test-user-789",
            task="Test task for agent execution",
            correlation_id="test-corr-abc",
            source_service="test-service"
        )

    @pytest.mark.asyncio
    async def test_execute_agent_basic(self, agent_executor, sample_execution_request):
        """Test basic agent execution functionality."""
        # Execute the agent
        result = await agent_executor.execute_agent(sample_execution_request)
        
        # Verify response structure
        assert result.execution_id == sample_execution_request.execution_id
        assert result.correlation_id == sample_execution_request.correlation_id
        assert result.status == ExecutionStatus.COMPLETED
        assert result.result is not None
        assert result.execution_time > 0
        assert len(result.messages) > 0

    @pytest.mark.asyncio
    async def test_send_status_update(self, agent_executor, mock_producer):
        """Test status update functionality."""
        # Send status update
        await agent_executor.send_status_update(
            execution_id="test-123",
            status=ExecutionStatus.RUNNING,
            message="Test status update",
            correlation_id="corr-123"
        )
        
        # Verify producer was called
        mock_producer.send.assert_called_once()
        call_args = mock_producer.send.call_args
        assert call_args[0][0] == "agent-execution-status"  # topic
        assert "execution_id" in call_args[1]["value"]
        assert call_args[1]["value"]["status"] == "running"


class TestHealthService:
    """Test cases for HealthService functionality."""

    @pytest.fixture
    def health_service(self):
        """Create HealthService instance for testing."""
        return HealthService()

    @pytest.mark.asyncio
    async def test_get_health_status(self, health_service):
        """Test health status endpoint."""
        status = await health_service.get_health_status()
        
        # Verify response structure
        assert "status" in status
        assert "timestamp" in status
        assert "uptime_seconds" in status
        assert "service" in status
        assert "checks" in status
        assert status["service"] == "agent-executor-service"

    @pytest.mark.asyncio
    async def test_get_readiness_status(self, health_service):
        """Test readiness status endpoint."""
        status = await health_service.get_readiness_status()
        
        # Verify response structure
        assert "ready" in status
        assert "timestamp" in status
        assert "checks" in status
        assert isinstance(status["ready"], bool)


class TestDataModels:
    """Test cases for data models."""

    def test_agent_execution_request_creation(self):
        """Test AgentExecutionRequest model creation."""
        request = AgentExecutionRequest(
            execution_id="test-123",
            agent_id="agent-456",
            user_id="user-789",
            task="Test task",
            correlation_id="corr-abc",
            source_service="test-service"
        )
        
        assert request.execution_id == "test-123"
        assert request.agent_id == "agent-456"
        assert request.priority == 5  # default value
        assert request.timeout == 300  # default value
        assert isinstance(request.created_at, datetime)

    def test_agent_execution_request_validation(self):
        """Test AgentExecutionRequest validation."""
        # Test invalid priority
        with pytest.raises(ValueError):
            AgentExecutionRequest(
                execution_id="test-123",
                agent_id="agent-456",
                user_id="user-789",
                task="Test task",
                priority=15,  # Invalid: > 10
                correlation_id="corr-abc",
                source_service="test-service"
            )
        
        # Test invalid timeout
        with pytest.raises(ValueError):
            AgentExecutionRequest(
                execution_id="test-123",
                agent_id="agent-456",
                user_id="user-789",
                task="Test task",
                timeout=10,  # Invalid: < 30
                correlation_id="corr-abc",
                source_service="test-service"
            )


if __name__ == "__main__":
    pytest.main([__file__])
