# test_kafka_api.py (Updated for new endpoints and features)

import requests
import json
import argparse

# --- Configuration ---
API_GATEWAY_URL = "http://localhost:8000/api/v1"


# --- Helper Functions ---
def send_api_request(endpoint, json_payload):
    """Sends a POST/GET request to the specified API endpoint and handles responses."""
    url = f"{API_GATEWAY_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}
    try:
        response = requests.post(url, headers=headers, json=json_payload)
        response.raise_for_status()
        return response
    except requests.exceptions.HTTPError as e:
        print(f"HTTP Error: {e}")
        if e.response and e.response.content:
            try:
                error_json = e.response.json()
                print("--- Error Response Body (JSON) ---")
                print(json.dumps(error_json, indent=4))
            except json.JSONDecodeError:
                print("--- Raw Error Response Body ---")
                print(e.response.text)
        else:
            print("No error response body received.")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Request Exception: {e}")
        return None


def test_sse_stream(correlation_id):
    """Streams SSE events for a given correlation ID using GET."""
    url = f"{API_GATEWAY_URL}/workflow-execute/stream/{correlation_id}"
    try:
        with requests.get(url, stream=True) as response:
            response.raise_for_status()
            print(f"--- SSE Stream for correlationId: {correlation_id} ---")
            for line in response.iter_lines():
                if line:
                    if line.startswith(b"data:"):
                        sse_data = line[5:].strip()
                        try:
                            response_json = json.loads(sse_data.decode("utf-8"))
                            print("SSE Event Data:", json.dumps(response_json, indent=4))
                        except json.JSONDecodeError:
                            print(
                                f"Warning: Could not decode JSON from SSE data: {sse_data.decode('utf-8')}"
                            )
                    else:
                        print(f"SSE Event (non-data): {line.decode('utf-8')}")
    except requests.exceptions.RequestException as e:
        print(f"Request Exception during SSE stream: {e}")


# --- Test Functions for Each Endpoint ---


def test_start_workflow(workflow_id, approval=False):
    """Tests the POST /workflow-requests endpoint."""
    workflow_request_payload = {
        "workflow_id": workflow_id,
        "approval": approval,
        "payload": {
            "user_dependent_fields": ["topic", "video_type"],
            "user_payload_template": {
                "topic": "NVIDIA latest Event",
                "video_type": "SHORT",
            },
        },
    }
    print("\n--- Test Start Workflow Endpoint (POST /workflow-execute/workflow-requests) ---")
    response = send_api_request("/workflow-execute/execute", workflow_request_payload)
    if response:
        print("--- Response Headers ---")
        print(json.dumps(dict(response.headers), indent=4))
        try:
            response_json = response.json()
            print("--- Response Body (JSON) ---")
            print(json.dumps(response_json, indent=4))
            correlation_id = response_json.get("correlationId")
            if correlation_id:
                if approval:
                    print(
                        f"\nWorkflow started with approval, correlationId: {correlation_id}. Waiting for approval... (Test SSE Stream using correlationId)"
                    )
                    test_sse_stream(correlation_id)
                else:
                    print(f"\nWorkflow started (no approval), correlationId: {correlation_id}.")
                    test_sse_stream(correlation_id)
        except json.JSONDecodeError:
            print("--- Response Body (Text) ---")
            print(response.text)


def test_execution_request(correlation_id, action, node_id=None, params=None):
    """Tests the POST /workflow-execute/execution-requests endpoint."""
    execution_request_payload = {
        "correlationId": correlation_id,
        "action": action,
        "data": {
            "workflow_id": correlation_id,
            "approval": True,
            "payload": {
                "user_dependent_fields": ["video_type"],
                "user_payload_template": {
                    "video_type": "SHORT",
                },
            },
        },
        "node_id": node_id,
        "params": params,
    }
    print("\n--- Test Execution Request Endpoint (POST /workflow-execute/execution-requests) ---")
    response = send_api_request("/workflow-execute/re-execute", execution_request_payload)
    if response:
        print("--- Response Headers ---")
        print(json.dumps(dict(response.headers), indent=4))
        try:
            print("--- Response Body (JSON) ---")
            print(json.dumps(response.json(), indent=4))
        except json.JSONDecodeError:
            print("--- Response Body (Text) ---")
            print(response.text)


def test_approval_request(correlation_id, decision):
    """Tests the POST /kafka/approval-requests endpoint."""
    approval_request_payload = {"correlationId": correlation_id, "decision": decision}
    print("\n--- Test Approval Request Endpoint (POST /workflow-execute/approval-requests) ---")
    response = send_api_request("/workflow-execute/approve", approval_request_payload)
    if response:
        print("--- Response Headers ---")
        print(json.dumps(dict(response.headers), indent=4))
        try:
            print("--- Response Body (JSON) ---")
            print(json.dumps(response.json(), indent=4))
        except json.JSONDecodeError:
            print("--- Response Body (Text) ---")
            print(response.text)


def test_stream_endpoint(correlation_id):
    """Tests the GET /workflow-execute/stream/{correlation_id} endpoint (SSE Stream)."""
    print("\n--- Test Stream Endpoint (GET /workflow-execute/stream/{correlation_id}) ---")
    test_sse_stream(correlation_id)


# --- Main execution ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Kafka API Testing Script")
    parser.add_argument(
        "test_type",
        choices=["start_workflow", "execution_request", "approval_request", "stream_endpoint"],
        help="Type of test to run",
    )
    parser.add_argument("--workflow_id", help="Workflow ID for start_workflow test")
    parser.add_argument(
        "--approval",
        action="store_true",
        help="Include approval flag for start_workflow test (defaults to false)",
    )
    parser.add_argument(
        "--correlation_id", help="Correlation ID for execution/approval/stream requests"
    )
    parser.add_argument(
        "--action", help="Action type for execution requests (retry, regenerate, re-execute)"
    )
    parser.add_argument("--node_id", help="Server ID for regenerate action in execution requests")
    parser.add_argument(
        "--params", help="Parameters (JSON string) for regenerate action in execution requests"
    )
    parser.add_argument("--decision", help="Decision (approve/reject) for approval requests")

    args = parser.parse_args()

    if args.test_type == "start_workflow":
        test_start_workflow(args.workflow_id, args.approval)
    elif args.test_type == "execution_request":
        params = json.loads(args.params) if args.params else None
        test_execution_request(args.correlation_id, args.action, args.node_id, params)
    elif args.test_type == "approval_request":
        test_approval_request(args.correlation_id, args.decision)
    elif args.test_type == "stream_endpoint":
        test_stream_endpoint(args.correlation_id)
