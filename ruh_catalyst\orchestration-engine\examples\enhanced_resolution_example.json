{"nodes": [{"id": "text_extractor_node", "server_script_path": "path/to/text_extractor", "server_tools": [{"tool_id": 1, "tool_name": "extract_text", "input_schema": {"type": "object", "properties": {"document_url": {"type": "string"}}}, "output_schema": {"type": "object", "properties": {"result": {"type": "string"}}}}]}, {"id": "sentiment_analyzer_node", "server_script_path": "path/to/sentiment_analyzer", "server_tools": [{"tool_id": 1, "tool_name": "analyze_sentiment", "input_schema": {"type": "object", "properties": {"text": {"type": "string"}}}, "output_schema": {"type": "object", "properties": {"result": {"type": "object", "properties": {"sentiment": {"type": "string"}, "confidence": {"type": "number"}}}}}}]}, {"id": "summary_generator_node", "server_script_path": "path/to/summary_generator", "server_tools": [{"tool_id": 1, "tool_name": "generate_summary", "input_schema": {"type": "object", "properties": {"text": {"type": "string"}, "sentiment": {"type": "string"}}}, "output_schema": {"type": "object", "properties": {"result": {"type": "string"}}}}]}], "transitions": [{"id": "extract_text_transition", "sequence": 1, "transition_type": "initial", "execution_type": "MCP", "node_info": {"node_id": "text_extractor_node", "tools_to_use": [{"tool_id": 1, "tool_name": "extract_text", "tool_params": {"items": [{"field_name": "document_url", "field_value": "https://example.com/document.pdf"}]}}], "input_data": [], "output_data": [{"to_transition_id": "analyze_sentiment_transition", "target_node_id": "sentiment_analyzer_node", "data_type": "string", "output_handle": "extracted_text_handle", "output_metadata": {"field_mappings": {"extracted_text": "result"}}}, {"to_transition_id": "generate_summary_transition", "target_node_id": "summary_generator_node", "data_type": "string", "output_handle": "text_for_summary_handle"}]}, "end": false}, {"id": "analyze_sentiment_transition", "sequence": 2, "transition_type": "standard", "execution_type": "MCP", "node_info": {"node_id": "sentiment_analyzer_node", "tools_to_use": [{"tool_id": 1, "tool_name": "analyze_sentiment", "tool_params": {"items": [{"field_name": "text", "field_value": "${extracted_text}"}]}}], "input_data": [{"from_transition_id": "extract_text_transition", "output_handle": "extracted_text_handle", "mapping": [{"from_field": "result", "to_field": "extracted_text"}]}], "output_data": [{"to_transition_id": "generate_summary_transition", "target_node_id": "summary_generator_node", "data_type": "object", "output_handle": "sentiment_analysis_handle", "output_metadata": {"field_mappings": {"sentiment_data": "result.sentiment", "confidence_score": "result.confidence"}}}]}, "end": false}, {"id": "generate_summary_transition", "sequence": 3, "transition_type": "standard", "execution_type": "MCP", "node_info": {"node_id": "summary_generator_node", "tools_to_use": [{"tool_id": 1, "tool_name": "generate_summary", "tool_params": {"items": [{"field_name": "text", "field_value": "${source_text}"}, {"field_name": "sentiment", "field_value": "${sentiment_value}"}]}}], "input_data": [{"from_transition_id": "extract_text_transition", "output_handle": "text_for_summary_handle", "mapping": [{"from_field": "result", "to_field": "source_text"}]}, {"from_transition_id": "analyze_sentiment_transition", "output_handle": "sentiment_analysis_handle", "mapping": [{"from_field": "result.sentiment", "to_field": "sentiment_value"}]}], "output_data": []}, "end": true}]}