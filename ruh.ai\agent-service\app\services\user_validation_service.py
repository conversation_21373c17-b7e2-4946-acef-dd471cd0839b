from sqlalchemy import Column, String, Integer  # Import necessary SQLAlchemy components
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import Session

Base = declarative_base()
class User(Base):
    __tablename__ = "users"  # Replace with the correct table name
    id = Column(String, primary_key=True)  # Assuming User ID is a string
    # You might add other necessary columns, but 'id' is essential for validation
    

def validate_user_id(user_id: str, db: Session) -> bool:
        """
        Validates the existence of a user ID by querying the users table directly.

        Args:
            user_id: The user ID to validate.
            db: The SQLAlchemy database session.

        Returns:
            True if the user ID exists, False otherwise.
        """
        try:
            user = db.query(User).filter(User.id == user_id).first()
            print(f"[USER] : {user}")

            if user:
                 return True
            else:
                 return False
        except Exception as e:
            print(f"[ERROR] Error validating user ID in the database: {e}") 
            return f"rror validating user ID in the database: {e}"
