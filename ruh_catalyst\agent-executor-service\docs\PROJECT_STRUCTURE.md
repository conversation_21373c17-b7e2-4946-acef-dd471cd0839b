# Agent Executor Service - Project Structure

## Recommended Directory Structure

```
agent-executor-service/
├── app/                          # Main application code
│   ├── __init__.py
│   ├── main.py                   # FastAPI application entry point
│   ├── api/                      # API layer
│   │   ├── __init__.py
│   │   ├── dependencies.py       # API dependencies and middleware
│   │   ├── routes/               # API route definitions
│   │   │   ├── __init__.py
│   │   │   ├── executions.py     # Execution management endpoints
│   │   │   ├── health.py         # Health check endpoints
│   │   │   ├── metrics.py        # Metrics endpoints
│   │   │   └── resources.py      # Resource management endpoints
│   │   └── middleware/           # Custom middleware
│   │       ├── __init__.py
│   │       ├── auth.py           # Authentication middleware
│   │       ├── logging.py        # Request logging middleware
│   │       └── rate_limit.py     # Rate limiting middleware
│   ├── core/                     # Core business logic
│   │   ├── __init__.py
│   │   ├── config.py             # Configuration management
│   │   ├── exceptions.py         # Custom exceptions
│   │   ├── logging.py            # Logging configuration
│   │   └── security.py           # Security utilities
│   ├── services/                 # Service layer
│   │   ├── __init__.py
│   │   ├── execution_service.py  # Execution management service
│   │   ├── kafka_service.py      # Kafka integration service
│   │   ├── resource_service.py   # Resource management service
│   │   ├── scheduler_service.py  # Task scheduling service
│   │   └── monitoring_service.py # Monitoring and metrics service
│   ├── models/                   # Data models
│   │   ├── __init__.py
│   │   ├── database.py           # Database models (SQLAlchemy)
│   │   ├── schemas.py            # Pydantic schemas
│   │   └── enums.py              # Enumeration definitions
│   ├── repositories/             # Data access layer
│   │   ├── __init__.py
│   │   ├── base.py               # Base repository class
│   │   ├── execution_repo.py     # Execution data access
│   │   └── metrics_repo.py       # Metrics data access
│   ├── integrations/             # External service integrations
│   │   ├── __init__.py
│   │   ├── kafka_client.py       # Kafka client wrapper
│   │   ├── agent_platform.py     # Agent platform integration
│   │   └── monitoring.py         # Monitoring system integration
│   └── utils/                    # Utility functions
│       ├── __init__.py
│       ├── correlation.py        # Correlation ID utilities
│       ├── datetime.py           # Date/time utilities
│       └── validation.py         # Validation utilities
├── tests/                        # Test suite
│   ├── __init__.py
│   ├── conftest.py               # Pytest configuration
│   ├── unit/                     # Unit tests
│   │   ├── __init__.py
│   │   ├── test_services/
│   │   ├── test_models/
│   │   └── test_utils/
│   ├── integration/              # Integration tests
│   │   ├── __init__.py
│   │   ├── test_api/
│   │   ├── test_kafka/
│   │   └── test_database/
│   └── e2e/                      # End-to-end tests
│       ├── __init__.py
│       └── test_execution_flow.py
├── migrations/                   # Database migrations
│   ├── versions/
│   └── alembic.ini
├── docker/                       # Docker configuration
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.dev.yml
├── k8s/                          # Kubernetes manifests
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── configmap.yaml
│   ├── secret.yaml
│   └── hpa.yaml
├── scripts/                      # Utility scripts
│   ├── setup.sh                  # Environment setup
│   ├── run_tests.sh              # Test runner
│   ├── migrate.sh                # Database migration
│   └── deploy.sh                 # Deployment script
├── docs/                         # Documentation
│   ├── api/                      # API documentation
│   ├── deployment/               # Deployment guides
│   └── development/              # Development guides
├── .env.example                  # Environment variables template
├── .gitignore                    # Git ignore rules
├── .dockerignore                 # Docker ignore rules
├── requirements.txt              # Python dependencies
├── requirements-dev.txt          # Development dependencies
├── pyproject.toml                # Poetry configuration
├── README.md                     # Project documentation
├── TECHNICAL_SPECIFICATION.md   # Technical specification
└── PROJECT_STRUCTURE.md         # This file
```

## Key Files and Their Purposes

### Application Entry Points

#### `app/main.py`

```python
from fastapi import FastAPI
from app.api.routes import executions, health, metrics, resources
from app.core.config import get_settings
from app.core.logging import setup_logging

def create_app() -> FastAPI:
    settings = get_settings()
    setup_logging(settings.log_level)
    
    app = FastAPI(
        title="Agent Executor Service",
        description="Interface for agent execution management",
        version="1.0.0"
    )
    
    # Include routers
    app.include_router(executions.router, prefix="/api/v1")
    app.include_router(health.router, prefix="/api/v1")
    app.include_router(metrics.router, prefix="/api/v1")
    app.include_router(resources.router, prefix="/api/v1")
    
    return app

app = create_app()
```

### Configuration Management

#### `app/core/config.py`

```python
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str
    database_pool_size: int = 20
    
    # Kafka
    kafka_bootstrap_servers: str
    kafka_agent_request_topic: str = "agent-requests"
    kafka_agent_response_topic: str = "agent-responses"
    kafka_consumer_group: str = "agent-executor-service"
    
    # API
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    
    # Security
    secret_key: str
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Resource Management
    max_concurrent_executions: int = 100
    default_execution_timeout: int = 300
    
    # Monitoring
    prometheus_port: int = 9090
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"

def get_settings() -> Settings:
    return Settings()
```

### Service Layer Structure

#### `app/services/execution_service.py`

```python
from typing import Optional, List
from app.models.schemas import ExecutionRequest, ExecutionStatus
from app.repositories.execution_repo import ExecutionRepository
from app.integrations.kafka_client import KafkaClient

class ExecutionService:
    def __init__(
        self,
        execution_repo: ExecutionRepository,
        kafka_client: KafkaClient
    ):
        self.execution_repo = execution_repo
        self.kafka_client = kafka_client
    
    async def create_execution(
        self, 
        request: ExecutionRequest
    ) -> str:
        """Create a new execution request"""
        pass
    
    async def get_execution_status(
        self, 
        execution_id: str
    ) -> Optional[ExecutionStatus]:
        """Get execution status by ID"""
        pass
    
    async def cancel_execution(
        self, 
        execution_id: str
    ) -> bool:
        """Cancel an execution"""
        pass
    
    async def list_executions(
        self, 
        user_id: str, 
        status: Optional[str] = None
    ) -> List[ExecutionStatus]:
        """List executions for a user"""
        pass
```

### Data Models

#### `app/models/schemas.py`

```python
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime
from app.models.enums import ExecutionStatusEnum

class ExecutionRequest(BaseModel):
    agent_id: str = Field(..., description="Agent identifier")
    task_description: str = Field(..., description="Task to execute")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(default=1, ge=1, le=10)
    timeout: int = Field(default=300, ge=30, le=3600)
    callback_url: Optional[str] = None
    user_id: str = Field(..., description="User identifier")

class ExecutionStatus(BaseModel):
    execution_id: str
    status: ExecutionStatusEnum
    progress: float = Field(ge=0.0, le=1.0)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None

class ExecutionResult(BaseModel):
    execution_id: str
    result: Dict[str, Any]
    metadata: Dict[str, Any]
    execution_time: float
```

#### `app/models/enums.py`

```python
from enum import Enum

class ExecutionStatusEnum(str, Enum):
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class PriorityEnum(int, Enum):
    LOW = 1
    NORMAL = 5
    HIGH = 10
```

### API Routes Structure

#### `app/api/routes/executions.py`

```python
from fastapi import APIRouter, Depends, HTTPException
from app.models.schemas import ExecutionRequest, ExecutionStatus
from app.services.execution_service import ExecutionService
from app.api.dependencies import get_execution_service, get_current_user

router = APIRouter(prefix="/executions", tags=["executions"])

@router.post("/", response_model=dict)
async def create_execution(
    request: ExecutionRequest,
    service: ExecutionService = Depends(get_execution_service),
    current_user: str = Depends(get_current_user)
):
    """Create a new execution request"""
    execution_id = await service.create_execution(request)
    return {"execution_id": execution_id}

@router.get("/{execution_id}", response_model=ExecutionStatus)
async def get_execution_status(
    execution_id: str,
    service: ExecutionService = Depends(get_execution_service),
    current_user: str = Depends(get_current_user)
):
    """Get execution status"""
    status = await service.get_execution_status(execution_id)
    if not status:
        raise HTTPException(status_code=404, detail="Execution not found")
    return status
```

### Testing Structure

#### `tests/conftest.py`

```python
import pytest
import asyncio
from httpx import AsyncClient
from app.main import create_app
from app.core.config import get_settings

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def app():
    """Create application for testing."""
    return create_app()

@pytest.fixture
async def client(app):
    """Create test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

@pytest.fixture
def settings():
    """Get test settings."""
    return get_settings()
```

### Docker Configuration

#### `docker/Dockerfile`

```dockerfile
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/
COPY migrations/ ./migrations/

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Kubernetes Configuration

#### `k8s/deployment.yaml`

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-executor-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-executor-service
  template:
    metadata:
      labels:
        app: agent-executor-service
    spec:
      containers:
      - name: agent-executor-service
        image: agent-executor-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: agent-executor-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Development Workflow

### Initial Setup

1. Clone repository
2. Copy `.env.example` to `.env` and configure
3. Install dependencies: `pip install -r requirements-dev.txt`
4. Run database migrations: `./scripts/migrate.sh`
5. Start development server: `uvicorn app.main:app --reload`

### Testing

1. Run unit tests: `pytest tests/unit/`
2. Run integration tests: `pytest tests/integration/`
3. Run all tests with coverage: `./scripts/run_tests.sh`

### Deployment

1. Build Docker image: `docker build -f docker/Dockerfile -t agent-executor-service .`
2. Deploy to Kubernetes: `kubectl apply -f k8s/`
3. Monitor deployment: `kubectl get pods -l app=agent-executor-service`

This structure provides a solid foundation for building a scalable, maintainable agent executor service that follows Python best practices and modern application architecture patterns.
