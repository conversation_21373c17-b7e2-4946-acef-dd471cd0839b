"""
<PERSON><PERSON><PERSON> to test the database connection.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to sys.path
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

print("Testing database connection...")

from app.db.database import engine, Base, logger
from app.db.models import OAuthCredential
from sqlalchemy import inspect, text


def test_connection():
    """Test the database connection and print table information."""
    try:
        # Test connection by executing a simple query
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print(f"Connection test: {result.scalar() == 1}")

        # Get inspector
        inspector = inspect(engine)

        # Check if the table exists
        tables = inspector.get_table_names()
        print(f"Tables in database: {tables}")

        if "oauth_credentials" in tables:
            print("oauth_credentials table exists")
            # Get columns
            columns = inspector.get_columns("oauth_credentials")
            print("Columns:")
            for column in columns:
                print(f"  - {column['name']} ({column['type']})")
        else:
            print("oauth_credentials table does not exist, creating tables...")
            Base.metadata.create_all(bind=engine)
            print("Tables created successfully")

        print("Database connection test completed successfully")
        return True
    except Exception as e:
        print(f"Error testing database connection: {e}")
        return False


if __name__ == "__main__":
    test_connection()
