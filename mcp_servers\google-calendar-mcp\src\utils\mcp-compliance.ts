/**
 * MCP Protocol Compliance Utilities
 * Ensures HTTP wrapper maintains MCP protocol standards
 */

import { McpError } from "@modelcontextprotocol/sdk/types.js";

// MCP Error Codes (from JSON-RPC 2.0 specification)
export const MCP_ERROR_CODES = {
  PARSE_ERROR: -32700,
  INVALID_REQUEST: -32600,
  METHOD_NOT_FOUND: -32601,
  INVALID_PARAMS: -32602,
  INTERNAL_ERROR: -32603,
  // MCP-specific error codes
  AUTHENTICATION_REQUIRED: -32002,
  AUTHORIZATION_FAILED: -32003,
  RESOURCE_NOT_FOUND: -32004,
  RATE_LIMITED: -32005,
} as const;

export interface MCPErrorResponse {
  error: string;
  code: number;
  data?: any;
  requestId?: string;
}

export interface MCPSuccessResponse {
  result: any;
  requestId?: string;
}

export interface MCPStreamEvent {
  type: 'start' | 'progress' | 'result' | 'error' | 'end';
  data?: any;
  error?: string;
  requestId: string;
  timestamp: string;
}

/**
 * Creates an MCP-compliant error response
 */
export function createMCPError(
  code: number,
  message: string,
  data?: any,
  requestId?: string
): MCPErrorResponse {
  return {
    error: message,
    code,
    data,
    requestId,
  };
}

/**
 * Creates an MCP-compliant success response
 */
export function createMCPSuccess(
  result: any,
  requestId?: string
): MCPSuccessResponse {
  return {
    result,
    requestId,
  };
}

/**
 * Creates an SSE event for MCP streaming
 */
export function createSSEEvent(
  type: MCPStreamEvent['type'],
  requestId: string,
  data?: any,
  error?: string
): string {
  const event: MCPStreamEvent = {
    type,
    requestId,
    timestamp: new Date().toISOString(),
    ...(data && { data }),
    ...(error && { error }),
  };
  
  return `data: ${JSON.stringify(event)}\n\n`;
}

/**
 * Validates MCP request format
 */
export function validateMCPRequest(body: any): { valid: boolean; error?: string } {
  if (!body || typeof body !== 'object') {
    return { valid: false, error: 'Request body must be an object' };
  }

  if (!body.params || typeof body.params !== 'object') {
    return { valid: false, error: 'Request must have params object' };
  }

  return { valid: true };
}

/**
 * Converts standard errors to MCP errors
 */
export function toMCPError(error: unknown, requestId?: string): MCPErrorResponse {
  if (error instanceof McpError) {
    return createMCPError(error.code, error.message, error.data, requestId);
  }

  if (error instanceof Error) {
    // Map common error types to MCP error codes
    if (error.message.toLowerCase().includes('authentication')) {
      return createMCPError(
        MCP_ERROR_CODES.AUTHENTICATION_REQUIRED,
        error.message,
        undefined,
        requestId
      );
    }
    
    if (error.message.toLowerCase().includes('not found')) {
      return createMCPError(
        MCP_ERROR_CODES.RESOURCE_NOT_FOUND,
        error.message,
        undefined,
        requestId
      );
    }

    if (error.message.toLowerCase().includes('invalid')) {
      return createMCPError(
        MCP_ERROR_CODES.INVALID_PARAMS,
        error.message,
        undefined,
        requestId
      );
    }

    return createMCPError(
      MCP_ERROR_CODES.INTERNAL_ERROR,
      error.message,
      undefined,
      requestId
    );
  }

  return createMCPError(
    MCP_ERROR_CODES.INTERNAL_ERROR,
    'Unknown error occurred',
    undefined,
    requestId
  );
}

/**
 * Adds pagination support to responses
 */
export function addPagination<T>(
  items: T[],
  cursor?: string,
  limit?: number
): { items: T[]; nextCursor?: string } {
  if (!limit || limit <= 0) {
    return { items };
  }

  const startIndex = cursor ? parseInt(cursor, 10) || 0 : 0;
  const endIndex = startIndex + limit;
  const paginatedItems = items.slice(startIndex, endIndex);
  
  const nextCursor = endIndex < items.length ? endIndex.toString() : undefined;

  return {
    items: paginatedItems,
    nextCursor,
  };
}
