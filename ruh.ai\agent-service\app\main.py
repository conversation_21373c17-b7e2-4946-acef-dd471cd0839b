import os
import grpc
from concurrent import futures

from app.services.agent_service import AgentService
from app.grpc import agent_pb2_grpc


def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    # Add agent service to server
    agent_service = AgentService()
    agent_pb2_grpc.add_AgentServiceServicer_to_server(agent_service, server)

    # Get port from environment or use default
    port = os.getenv('PORT', '50057')  # Changed to a different default port (50057) to avoid collision.
    server.add_insecure_port(f'[::]:{port}')

    # Start server
    server.start()
    print(f"Agent service started on port {port}")

    # Keep thread alive
    server.wait_for_termination()

if __name__ == '__main__':
    serve()